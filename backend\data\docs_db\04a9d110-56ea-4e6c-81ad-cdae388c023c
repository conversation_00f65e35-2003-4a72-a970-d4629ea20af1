## 3.2.1. Losses based on p-norms

Losses based on p -norms are the simplest ones, and comprise the mean squared error ( MSE ) (for p = 2) and the mean absolute error ( MAE ) (for p = 1).

$$M S E ( X, Y ; \theta ) = - \sum _ { i = 1 } ^ { N } \| y _ { i } - \hat { y } _ { i } \| _ { 2 },$$

$$\text{MAE} ( X, Y ; \theta ) = - \sum _ { i = 1 } ^ { N } \| y _ { i } - \hat { y } _ { i } \| _ { 1 }.$$

In GAN s, to regularize the segmentations produced by the generator, it is common to utilize hybrid losses containing MSE ( ' 2 loss) (<PERSON><PERSON> et al., 2019) or MAE ( ' 1 loss) (<PERSON><PERSON> et al., 2019; <PERSON> et al., 2019; <PERSON><PERSON> et al., 2020). The MSE has also been used as a regularizer to match attention and ground-truth maps (<PERSON><PERSON> et al., 2020a).


## 3.2.2. Cross-entropy Loss

Semantic segmentation may be viewed as classification at the pixel level, i.e., as assigning a class label to each pixel. From this perspective, minimizing the negative log-likelihoods of pixel-wise predictions (i.e., maximizing their likelihood) may be achieved

<!-- page_break -->

by minimizing a cross-entropy loss L ce :

$$\mathcal { L } _ { c e } ( X, Y ; \theta ) = - \sum _ { i = 1 } ^ { N } \sum _ { p \in \Omega _ { i } } y _ { i p } \log \hat { y } _ { i p } + ( 1 - y _ { i p } ) \log ( 1 - \hat { y } _ { i p } ), \ \hat { y } _ { i p } = P ( y _ { i p } = 1 | X ( i ) ; \theta ),$$

where GLYPH&lt;10&gt; i is the set of all image i pixels, P is the probability, xip is p th image pixel in i th image and, yip 2 f 0 1 ; g and ˆ yip 2 [0 ; 1] are respectively the true and the predicted labels of xip . The cross-entropy loss appears in the majority of deep skin lesion segmentation works, e.g., Song et al. (2019), Singh et al. (2019), and Zhang et al. (2019a).

Since the gradient of the cross-entropy loss function is inversely proportional to the predicted probabilities, hard-to-predict samples are weighted more in the parameter update equations, leading to faster convergence. A variant, the weighted crossentropy loss, penalizes pixels and class labels di GLYPH&lt;11&gt; erently. Nasr-Esfahani et al. (2019) used pixel weights inversely proportional to their distance to lesion boundaries to enforce sharper boundaries. Class weighting may also mitigate the class imbalance, which, left uncorrected, tends to bias models towards the background class, since lesions tend to occupy a relatively small portion of images. Chen et al. (2018b), Goyal et al. (2019a), and Wang et al. (2019b) apply such a correction, using class weights inversely proportional to the class pixel frequency. Mirikharaji et al. (2019) weighted the pixels according to annotation noise estimated using a set of cleanly annotated data. All the aforementioned losses treat pixels independently without enforcing spatial coherence, which motivates their combination with other consistency-seeking losses.