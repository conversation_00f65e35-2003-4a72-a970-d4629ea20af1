## 2.2. Synthetic Data Generation

Data augmentation-synthesizing new samples from existing ones-is commonly employed in the training of DL models. Augmented data serve as a regularizer, increase the amount and diversity of data (<PERSON><PERSON> and <PERSON>, 2019), induce desirable invariances on the model, and alleviate class imbalance. Traditional data augmentation applies simple geometric, photometric, and colorimetric transformations on the samples, including mirroring, translation, scaling, rotation, cropping, random region erasing, a GLYPH&lt;14&gt; ne or elastic deformation, modifications of hue, saturation, brightness, and contrast. Usually, several transformations are chosen at random and combined. Fig. 4 exemplifies the procedure, as applied to a dermoscopic image with Albumentations (<PERSON><PERSON><PERSON> et al., 2020), a state-of-the-art open-source library for image augmentation.

As mentioned earlier, augmented training data induce invariance on the models: random translations and croppings, for example, help induce a translation-invariant model. This has implications for skin lesion analysis, e.g., data augmentation for generic datasets (such as ImageNet (<PERSON><PERSON> et al., 2009)) forgo vertical mirroring and large-angle rotations, because natural scenes have a strong vertical anisotropy, while skin lesion images are isotropic. In addition, augmented test data (test-time augmentation) may also improve generalization by combining the predictions of several augmented samples through, for example, average pooling or majority voting (<PERSON><PERSON> and <PERSON>aar, 2019). <PERSON> et al. (2018) have systematically evaluated the e GLYPH&lt;11&gt; ect of several data

<!-- page_break -->

augmentation schemes for skin lesion classification, finding that the use of both training and test augmentation is critical for performance, surpassing, in some cases, increases of real data without augmentation. Valle et al. (2020) found, in a very large-scale experiment, that test-time augmentation was the second most influential factor for classification performance, after training set size. No systematic study of this kind exists for skin lesion segmentation.

picture_counter_13 The image shows a close-up view of a skin lesion. It has irregular borders and a mottled appearance with shades of brown and blue. Fine hairs are also visible.

picture_counter_14 The image shows a close-up view of a skin lesion, possibly a mole, with irregular borders, varying colors (brown and blue), and hair.

picture_counter_15 The image shows a close-up view of a skin lesion with irregular borders and varying shades of brown and blue. Hairs are visible within and around the lesion.

(a) Original

picture_counter_16 The image shows a close-up view of a skin lesion with irregular borders and varying shades of brown and black, potentially indicating a dermatological condition. Fine hairs are visible around the lesion.

(b) A GLYPH&lt;14&gt; ne deformation

(c) Elastic deformation

picture_counter_17 The image shows a close-up view of a skin lesion with irregular borders and varying shades of red, purple, and gray. Hair is also visible in the image.

(d) Histogram equalization

picture_counter_18 The image shows a close-up view of a skin lesion with irregular pigmentation and fine hairs.

(e) HSV shift

(f) RGB shift

Fig. 4: Various data augmentation transformations applied to a dermoscopic image (image source: ISIC 2016 dataset (Gutman et al., 2016)) using the Albumentations library (Buslaev et al., 2020).

Although traditional data augmentation is crucial for training DL models, it falls short of providing samples that are both diverse and plausible from the same distribution as real data. Thus, modern data augmentation (Tajbakhsh et al., 2020a) employs generative modeling, learning the probability distribution of the real data, and sampling from that distribution. Generative adversarial networks ( GAN s) (Goodfellow et al., 2020) are the most promising approach in this direction (Shorten and Khoshgoftaar, 2019), especially for medical image analysis (Yi et al., 2019; Kazeminia et al., 2020; Shamsolmoali et al., 2021). GAN s employ an adversarial training between a generator, which attempts to generate realistic fake samples, and a discriminator, which attempts to di GLYPH&lt;11&gt; erentiate real samples from fake ones. When the procedure converges, the generator output is surprisingly convincing, but GAN s are computationally expensive and di GLYPH&lt;14&gt; cult to train (Creswell et al., 2018).

Synthetic generation of skin lesions has received some recent interest, especially in the context of improving classification. Works can be roughly divided into those that use GAN s to create new images from a Gaussian latent variable (Baur et al., 2018; Pollastri et al., 2020; Abdelhalim et al., 2021), and those that implement GAN s based on image-to-image translation (Abhishek and Hamarneh, 2019; Bissoto et al., 2018; Ding et al., 2021).

Noise-based GAN s, such as DCGAN (Yu et al., 2017b), LAPGAN (Denton et al., 2015), and PGAN (Karras et al., 2018), learn to decode a Gaussian latent variable into an image that belongs to the training set distribution. The main advantage of these techniques

<!-- page_break -->

is the ability to create more, and more diverse images, as, in principle, any sample from a multivariate Gaussian distribution may become a di GLYPH&lt;11&gt; erent image. The disadvantage is that the images tend to be of lower quality, and, in the case of segmentation, one needs to generate plausible pairs of images and segmentation masks.

Image-to-image translation GAN s, such as pix2pix (Isola et al., 2017) and pix2pixHD (Wang et al., 2018), learn to create new samples from a semantic segmentation map. They have complementary advantages and disadvantages. Because the procedure is deterministic (one map creates one image), they have much less freedom in the number of samples available, but the images tend to be of higher quality (or more 'plausible'). There is no need to generate separate segmentation maps because the generated image is intrinsically compatible with the input segmentation map.

The two seminal papers on GAN s for skin lesions (Baur et al., 2018; Bissoto et al., 2018) evaluate several models. Baur et al. (2018) compare the noise-based DCGAN LAPGAN , , and PGAN for the generation of 256 GLYPH&lt;2&gt; 256-pixel images using both qualitative and quantitative criteria, finding that the PGAN gives considerably better results. They further examine the PGAN against a panel of human judges, composed by dermatologists and DL experts, in a 'visual Turing test', showing that both had di GLYPH&lt;14&gt; culties in distinguishing the fake images from the true ones. Bissoto et al. (2018) adapt the PGAN to be class-conditioned on diagnostic category, and the image-to-image pix2pixHD to employ the semantic annotation provided by the feature extraction task of the ISIC 2018 dataset (Section 1), comparing those to an unmodified DCGAN on 256 GLYPH&lt;2&gt; 256-pixel images, and finding the modified pix2pixHD to be qualitatively better. They use the performance improvement on a separate classification network as a quantitative metric, finding that the use of samples from both PGAN and pix2pixHD leads to the best improvements. They also showcase images of size up to 1 ; 024 GLYPH&lt;2&gt; 1 024 pixels generated by the pix2pixHD-derived model. ;

Pollastri et al. (2020) extended DCGAN and LAPGAN architectures to generate the segmentation masks (in the pairwise scheme explained above), making their work the only noise-based GAN s usable for segmentation to date. Bi et al. (2019a) introduced stacked adversarial learning to GAN s to learn class-specific skin lesion image generators given the ground-truth segmentations. Abhishek and Hamarneh (2019) employ pix2pix to translate a binary segmentation mask into a dermoscopic image and use the generated image-mask pairs to augment skin lesion segmentation training datasets, improving segmentation performance.

Ding et al. (2021) feed a segmentation mask and an instance mask to a conditional GAN generator, where the instance mask states the diagnostic category to be synthesized. In both cases, the discriminator receives di GLYPH&lt;11&gt; erent resolutions of the generated image and is required to make a decision for each of them. Abdelhalim et al. (2021) is a recent work that also conditions PGAN on the class label and uses the generated outputs to augment a melanoma diagnosis dataset.

Recently, Bissoto et al. (2021) cast doubt on the power of GAN -synthesized data augmentation to reliably improve skin lesion classification. Their evaluation, which included four GAN models, four datasets, and several augmentation scenarios, showed improvement only in a severe cross-modality scenario (training on dermoscopic and testing on clinical images). To the best of our knowledge, no corresponding systematic evaluation exists for skin lesion segmentation.


## 2.3. Supervised, Semi-supervised, Weakly supervised, Self-supervised learning

Although supervised DL has achieved outstanding performance in various medical image analysis applications, its dependency on high-quality annotations limits its applicability, as well as its generalizability to unseen, out-of-distribution data. Semi-supervised

<!-- page_break -->

Fig. 5: A breakdown of di GLYPH&lt;11&gt; erent levels of supervision used in the 177 surveyed works. Fully supervised models continue to make up the majority of the literature (163 papers), with semi-supervised and weakly supervised methods appearing in only 9 papers. Self-supervision in skin lesion segmentation is fairly new, with all the 5 papers appearing from 2020 onwards.

picture_counter_19 The image is a pie chart showing the distribution of data types: Fully-supervised (92.1%), Semi- and weakly (5.1%), and Self-supervised (2.8%).

techniques attempt to learn from both labeled and unlabeled samples. Weakly supervised techniques attempt to exploit partial annotations like image-level labels or bounding boxes, often in conjunction with a subset of pixel-level fully-annotated samples.

Since pixel-level annotation of skin lesion images is costly, there is a trade-o GLYPH&lt;11&gt; between annotation precision and e GLYPH&lt;14&gt; ciency. In practice, the annotations are intrinsically noisy, which can be modeled explicitly to avoid over-fitting. (We discuss the issue of annotation variability in Section 4.2.) To deal with label noise, Mirikharaji et al. (2019) learn a model robust to annotation noise, making use of a large set of unreliable annotations and a small set of perfect clean annotations. They propose to learn a spatially adaptive weight map corresponding to each training data, assigning di GLYPH&lt;11&gt; erent weights to noisy and clean pixel-level annotations while training the deep model. To remove the dependency on having a set of perfectly clean annotations, Redekop and Chernyavskiy (2021) propose to alter noisy ground-truth masks during training by considering the quantification of aleatoric uncertainty (Der Kiureghian and Ditlevsen, 2009; Gal, 2016; Depeweg et al., 2018; Kwon et al., 2020) to obtain a map of regions of high and low uncertainty. Pixels of ground-truth masks in highly uncertain regions are flipped, progressively increasing the model's robustness to label noise. Ribeiro et al. (2020) deal with noise by discarding inconsistent samples and annotation detail during training time, showing that the model generalizes better even when detailed annotations are required in test time.

When there is a labeled dataset, even if the number of labeled samples is far less than that of unlabeled samples, semi- and self-supervision techniques can be applied. Li et al. (2021c) propose a semi-supervised approach, using a transformation-consistent self-ensemble to leverage unlabeled data and to regularize the model. They minimize the di GLYPH&lt;11&gt; erence between the network predictions of di GLYPH&lt;11&gt; erent transformations (random perturbations, flipping, and rotation) applied to the input image and the transformation of the model prediction for the input image. Self-supervision attempts to exploit intrinsic labels by solving proxy tasks, enabling the use

<!-- page_break -->

of a large, unlabeled corpus of data to pretrain a model before fine-tuning it on the target task. An example is to artificially apply random rotations in the input images, and train the model to predict the exact degree of rotation (Gidaris et al., 2018). Note that the degree of rotation of each image is known, since it was artificially applied, and thus, can be used as a label during training. Similarly, for skin lesion segmentation, Li et al. (2020b) propose to exploit the color distribution information, the proxy task being to predict values from blue and red color channels while having the green one as input. They also include a task to estimate the red and blue color distributions to improve the model's ability to extract global features. After the pretraining, they use a smaller set of labeled data to fine-tune the model.