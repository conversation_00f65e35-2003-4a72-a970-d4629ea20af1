## 3.1.2. Multiple Network Models

Motivations for models comprising more than one DL sub-model are diverse, ranging from alleviating training noise and exploiting a diversity of features learned by di GLYPH&lt;11&gt; erent models to exploring synergies between multi-task learners. After examining the literature (Fig. 6), we further classified the works in this section into standard ensembles and multi-task models. We also discuss generative adversarial models, which are intrinsically multi-network models, in a separate category.

*******. Standard Ensembles. Ensemble models are widely used in machine learning, motivated by the hope that the complementarity of di GLYPH&lt;11&gt; erent models may lead to more stable combined predictions (<PERSON><PERSON> and <PERSON><PERSON>, 2018). Ensemble performance is contingent on the quality and diversity of the component models, which can be combined at the feature level (early fusion) or the prediction level (late fusion). The former combines the features extracted by the components and learns a meta-model on them, while the latter pools or combines the models' predictions with or without a meta-model.

All methods discussed in this section employ late fusion, except for an approach loosely related to early fusion (<PERSON> et al., 2019a), which explores various learning-rate decay schemes, and builds a single model by averaging the weights learned at di GLYPH&lt;11&gt; erent epochs to bypass poor local minima during training. Since the weights correspond to features learned by the convolution filters, this approach can be interpreted as feature fusion.

Most works employ a single DL architecture with multiple training routines, varying configurations more or less during training (Canalini et al., 2019). The changes between component models may involve network hyperparameters: number of filters per block and their size (Codella et al., 2017); optimization and regularization hyperparameters: learning rate, weight decay (Tan et al., 2019b); the training set: multiple splits of a training set (Yuan et al., 2017; Yuan and Lo, 2019), separate models per class (Bi et al., 2019b); preprocessing: di GLYPH&lt;11&gt; erent color spaces (Pollastri et al., 2020); di GLYPH&lt;11&gt; erent pretraining strategies to initialize feature extractors (Canalini et al., 2019); or di GLYPH&lt;11&gt; erent ways to initialize the network parameters (Cui et al., 2019). Test-time augmentation may also be seen as a form of inference-time ensembling (Chen et al., 2018b; Liu et al., 2019b; Jahanifar et al., 2018) that combines the outputs of multiple augmented images to generate a more reliable prediction.

Bi et al. (2019b) trained a separate DL model for each class, as well as a separate classification model. For inference, the classification model output is used to weight the outputs of the category-specific segmentation networks. In contrast, Soudani and Barhoumi (2019) trained a meta 'recommender' model to dynamically choose, for each input, a segmentation technique from the top five scorers in the ISIC 2017 challenge, although their proposition was validated on a very small test set (10% of ISIC 2017 test set).

Several works also ensemble di GLYPH&lt;11&gt; erent model architectures for skin lesion segmentation. Goyal et al. (2019b) investigate multiple fusion approaches to avoid severe errors from individual models, comparing the average-, maximum- and minimum-pooling of their outputs. A common assumption is that the component models of the ensemble are trained independently, but Bi et al. (2017b) cascaded the component models, i.e., used the output of one model as the input of the next (in association with the actual image

<!-- page_break -->

input). Thus, each model attempts to refine the segmentation obtained by the previous one. They consider not only the final model output, but all the outputs in the cascade, making the technique a legitimate ensemble.

*******. Multi-task Models. Multi-task models jointly address more than one goal, in the hope that synergies among the tasks will improve overall performance (Zhang and Yang, 2022). This can be particularly helpful in medical image analysis, wherein aggregating tasks may alleviate the issue of insu GLYPH&lt;14&gt; cient data or annotations. For skin lesions, a few multi-task models exploiting segmentation and classification have been proposed (Chen et al., 2018b; Li and Shen, 2018; Yang et al., 2018; Xie et al., 2020b; Jin et al., 2021).

The synergy between tasks may appear when their models share common relevant features. Li and Shen (2018) assume that all features are shareable between the tasks, and train a single fully convolutional residual network to assign class probabilities at the pixel level. They aggregate the class probability maps to estimate both lesion region and class by weighted averaging of probabilities for di GLYPH&lt;11&gt; erent classes inside the lesion area. Yang et al. (2018) learn an end-to-end model formed by a shared convolutional feature extractor followed by three task-specific branches (one to segment skin lesions, one to classify them as melanoma versus nonmelanoma, and one to classify them as seborrheic keratosis versus non-seborrheic keratosis.) Similarly, Chen et al. (2018b) add a common feature extractor and separate task heads, and introduce a learnable gate function that controls the flow of information between the tasks to model the latent relationship between two tasks.

Instead of using a single architecture for classification and segmentation, Xie et al. (2020b) and Jin et al. (2021) use three CNNs in sequence to perform a coarse segmentation, followed by classification and, finally, a fine segmentation. Instead of shared features, these works exploit sequential guidance, in which the output of each task improves the learning of the next. While Xie et al. (2020b) feed the output of each network to the next, assuming that the classification network is a diagnostic category and a class activation map (Zhou et al., 2016), Jin et al. (2021) introduce feature entanglement modules, which aggregate features learned by di GLYPH&lt;11&gt; erent networks.

All multi-task models discussed so far have results suggesting complementarity between classification and segmentation, but there is no clear advantage among these models. The segmentation of dermoscopic features (e.g., networks, globules, regression areas) combined with the other tasks is a promising avenue of research, which could bridge classification and segmentation, by fostering the extraction of features that 'see' the lesion as human specialists do.

We do not consider in the hybrid group, two-stage models in which segmentation is used as ancillary preprocessing to classification (Yu et al., 2017a; Codella et al., 2017; Gonzalez-Diaz, 2018; Al-Masni et al., 2020), since without mutual influence (sharing of losses or features) or feedback between the two tasks, there is no opportunity for synergy.

Vesal et al. (2018a) stressed the importance of object localization as an ancillary task for lesion delineation, in particular deploying FasterRCNN (Ren et al., 2015) to regress a bounding box to crop the lesions before training a SkinNet segmentation model. While this two-stage approach considerably improves the results, it is computationally expensive (a fast nonDL -based bounding box detection algorithm was proposed earlier by Celebi et al. (2009a)). Goyal et al. (2019a) employed ROI detection with a deep extreme cut to extract the extreme points of lesions (leftmost, rightmost, topmost, bottommost pixels) and feed them, in a new auxiliary channel, to a segmentation model.

<!-- page_break -->

3.1.2.3. Generative Adversarial Models. We discussed GAN s for synthesizing new samples, their main use in skin lesion analysis, in Section 2.2. In this section, we are interested in GAN s not for generating additional training samples, but for directly providing enhanced segmentation models. Adversarial training encourages high-order consistency in predicted segmentation by implicitly looking into the joint distribution of class labels and ground-truth segmentation masks.

Peng et al. (2019), Tu et al. (2019), Lei et al. (2020), and Izadi et al. (2018) use a U-Net-like generator that takes a dermoscopic image as input, and outputs the corresponding segmentation, while the discriminator is a traditional CNN which attempts to discriminate pairs of image and generated segmentation from pairs of image and ground-truth. The generator has to learn to correctly segment the lesion in order to fool the discriminator. Jiang et al. (2019) use the same scheme, with a dual discriminator. Lei et al. (2020) also employ a second discriminator that takes as input only segmentations (unpaired from input images).

Since the discriminator may trivially learn to recognize the generated masks due to the presence of continuous probabilities, instead of the sharp discrete boundaries of the ground-truths, Wei et al. (2019) and Tu et al. (2019) address this by pre-multiplying both the generated and real segmentations with the (normalized) input images before feeding them to the discriminator.

We discuss adversarial loss functions further in Section 3.2.8.


## 3.1.3. Hybrid Feature Models

Although the major strength of CNN s is their ability to learn meaningful image features without human intervention, a few works tried to combine the best of both worlds, with strategies ranging from employing pre- or postprocessing to enforce prior knowledge to adding hand-crafted features Providing the model with prior knowledge about the expected shape of skin lesionswhich is missing from CNN s-may improve the performance. Mirikharaji and Hamarneh (2018) encode shape information into an additional regularization loss, which penalizes segmentation maps that deviate from a star-shaped prior (Section 3.2.6).

Conditional random fields ( CRF s) use pixel-level color information models to refine the segmentation masks output by the CNN . While both Tschandl et al. (2019) and Adegun and Viriri (2020b) consider a single CNN , Qiu et al. (2020) combine the outputs of multiple CNN s into a single mask, before feeding it together with the input image to the CRF s. ¨ nver and Ayan (2019) U use GrabCut (Rother et al., 2004) to obtain the segmentation mask given the dermoscopy image and a region proposal obtained by the YOLO (Redmon et al., 2016) network. These methods regularize the CNN segmentation, which is mainly based on textural patterns, with expected priors based on the color of the pixels.

Works that combine hand-crafted features with CNN s follow two distinct approaches. The first consists of pre-filtering the input images to increase the contrast between the lesion and the surrounding skin. Techniques explored include local binary patterns ( LBP s) (Ross-Howe and Tizhoosh, 2018; Jayapriya and Jacob, 2020), wavelets (Ross-Howe and Tizhoosh, 2018), Laplacian pyramids (Pour and Seker, 2020), and Laplacian filtering (Saba et al., 2019). The second approach consists of predicting an additional segmentation mask to combine with the one generated by the CNN . Zhang et al. (2019b), for example, use LBP s to consider the textural patterns of skin lesions and guide the networks towards more refined segmentations. Bozorgtabar et al. (2017b) also employ LBP s combined with pixel-level color information to divide the dermoscopic image into superpixels, which are then scored as part of the lesion or the background. The score mask is then combined with the CNN output mask to compute the final segmentation mask. Despite the limited number of works devoted to integrating deep features with hand-crafted ones, the

<!-- page_break -->

results so far indicate that this may be a promising research direction.