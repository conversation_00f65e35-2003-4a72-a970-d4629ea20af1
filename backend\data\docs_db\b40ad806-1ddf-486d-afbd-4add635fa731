## Experimental setup

Keras Python deep learning library on top of TensorFlow was utilized for implementing CNN models on a machine with the following specification; an   Intel  Core™ i7 CPU@ 3.6 GHz with 32 GB RAM and a Titan × ® Pascal Graphics Processing Unit (GPU). Extensive experiments were carried out to obtain the best settings of the CNN models that achieve the best possible results. It is worth noting that the pre-processing for enhancing the images has been carried out using   MATLAB 18 software. ®

Both the full and segmented datasets with their enhanced versions have been used to train VGG19 and EfficientNetB0 CNN pre-trained models. The training was carried on with Adam optimizer, learning rate of 0.001, batch size of 32, and the number of epochs (10-30) epochs, SoftMax classifier. The fine-tuned pre-trained models were used for feature extraction; therefore, the weights of the pre-trained models were frozen, and they were not updated during the training to maintain ImageNet's initial weights. The top layers were fine-tuned to adjust the

Figure 4. The framework of the used methodology for Chest X-ray images classification.

picture_counter_23 The image is a diagram illustrating a transfer learning process for medical image analysis. It shows the flow of image data through different stages. The "Original & enhancements" section shows the original chest X-ray images and their enhanced versions (Enhanced HE, Enhanced CLAHE, and Complement). The "Segmented" section displays the segmented images. The image processing steps include "Multiplication" and "Mask." The data is then fed into two transfer learning models: VGG19 and EfficientNetB0.

<!-- page_break -->

network according to the used chest X-ray data and to the current problem output which is (2/4) rather than 1000 in the ImageNet data. To avoid overfitting, a dropout of 0.3 was applied in the fully connected layers. Figures 5 and 6 illustrate the fine-tuned top layers based on VGG19 and EfficientNetB0 respectively for binary classification.


## Performance evaluation

A benchmark dataset was employed to validate the performance of the proposed framework. For binary classification, a set of 1600 X-ray images (800 of each class) have been used to train CNN models using transfer learning. The dataset has been divided into three subsets training, validation, and test sets. The training set is used for learning the model and adjusting the parameters. The validation set is to test the model during the training phase and fine-tune the parameters. The test set is to evaluate the trained model. The division was done as 400 samples (25%) of the used X-ray images were selected randomly for testing (200 images for each class), and the remaining 75% samples were split again into training and validation splits (80-20%). For 4-classes classification, a set of 3200 X-ray images (800 of each class) have been used. Before training CNN models, different preprocessing steps were implemented to enhance the images of both full and segmented lungs chest X-ray images to investigate the classification performance of the CNN models using the different versions.

The following metrics were used for the evaluation of the different CNN models trained using various dataset versions:

$$Sensitivity/Recall(%) = \frac { T P } { T P + F N }$$

$$\text{Precision} ( \%) = \frac { T p } { T P + F P }$$

$$\text{Specificity} ( \% ) = \frac { T N } { T N + FP }$$

$$\text{Accuracy} ( \%) = \frac { T P + T N } { T P + F P + T N + F N }$$

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_\_\_\_\_

==============================================================

| dense (Dense)     | (None, 1024)   |   25691136 |
|-------------------|----------------|------------|
| dense_1 (Dense)   | (None, 512)    |     524800 |
| dense_2 (Dense)   | (None, 256)    |     131328 |
| dropout (Dropout) | (None, 256)    |          0 |
| dense_3 (Dense)   | (None, 2)      |        514 |

Total params: 39,292,738

Trainable params: 26,347,778

Non-trainable params: 12,944,960

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_\_\_\_\_\_\_\_

Figure 5. Fine-tuned top layers based on VGG19 pre-training model.

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_

dense (Dense)                  (None, 1024)         5243904

dense\_1 (Dense)                (None, 512)          524800

dense\_2 (Dense)                (None, 256)          131328

dropout (Dropout)

(None, 256)          0

dense\_3 (Dense)                (None, 2)            514

=================================================================

Total params: 9,534,117

Trainable params: 5,900,546

Non-trainable params: 3,633,571

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_

Figure 6. Fine-tuned top layers based on EfficientNetB0 pre-training model.

Vol.:(0123456789)

<!-- page_break -->

Vol:.(1234567890)

$$F l s c o r e = \frac { 2 T P } { 2 T P + F P + F N }$$

$$( 5 )$$

where TP is a true-positive value, FP is a false-positive value, TN is a true-negative value and FN is a falsenegative value.