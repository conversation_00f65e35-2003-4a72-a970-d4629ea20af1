## 4.3. Evaluation Metrics

We can frame the skin lesion segmentation problem as a binary pixel-wise classification task, where the positive and negative classes correspond to the lesion and the background skin, respectively. Suppose that we have an input image and its corresponding segmentations: an automated segmentation ( AS ) produced by a segmentation algorithm and a manual segmentation ( MS ) outlined by a human expert. We can formulate a number of quantitative segmentation evaluation measures based on the concepts of true positive , false negative , false positive , and true negative , whose definitions are given in Table 2. In this table, actual and detected pixels refer to any given pixel in the MS and the corresponding pixel in the AS , respectively.

Table 2: Definitions of true positive, false negative, false positive, and true negative pixels in the context of skin lesion segmentation.


## Detected Pixel

|        |                         | Lesion ( + )   | Background ( GLYPH<0> )   |
|--------|-------------------------|----------------|---------------------------|
| Actual | Lesion ( + )            | True Positive  | False Negative            |
| Pixel  | Background ( GLYPH<0> ) | False Positive | True Negative             |

For a given pair of automated and manual segmentations, we can construct a 2 GLYPH&lt;2&gt; 2 confusion matrix (aka a contingency table (<PERSON>, 1904; Miller and Nicely, 1955)) C = GLYPH&lt;16&gt; TP FN FP TN GLYPH&lt;17&gt; , where TP FN FP , , , and TN denote the numbers of true positives, false negatives, false positives, and true negatives, respectively. Clearly, we have N = TP + FN + FP + TN , where N is the number of pixels in either image. Based on these quantities, we can define a variety of scalar similarity measures to quantify the accuracy of segmentation (Baldi et al., 2000; Japkowicz and Shah, 2011; Taha and Hanbury, 2015):

- GLYPH&lt;136&gt; Sensitivity ( SE ) and Specificity ( SP ) (Kahn, 1942; Yerushalmy, 1947; Binney et al., 2021): SE = TP TP + FN &amp; SP = TN TN + FP
- GLYPH&lt;136&gt; Precision ( PR ) and Recall ( RE ) (Kent et al., 1955): PR = TP TP + FP &amp; RE = TP TP + FN
- GLYPH&lt;136&gt; Accuracy ( AC ) = TP + TN TP + FN + FP + TN

<!-- page_break -->

- GLYPH&lt;136&gt; F-measure ( F ) (van Rijsbergen, 1979) = 2 j AS \ MS j j AS j + j MS j = 2 GLYPH&lt;1&gt; PR GLYPH&lt;1&gt; RE PR + RE = 2 TP 2 TP + FP + FN
- GLYPH&lt;136&gt; G-mean ( GM ) (Kubat et al., 1998) = p SE GLYPH&lt;1&gt; SP
- GLYPH&lt;136&gt; Balanced Accuracy ( BA ) (Chou and Fasman, 1978) = SE + SP 2
- GLYPH&lt;136&gt; Jaccard index ( J ) (Jaccard, 1901) = j AS \ MS j j AS [ MS j = TP TP + FN + FP
- GLYPH&lt;136&gt; Matthews Correlation Coe GLYPH&lt;14&gt; cient ( MCC ) (Matthews, 1975) = TP GLYPH&lt;1&gt; TN GLYPH&lt;0&gt; FP GLYPH&lt;1&gt; FN p ( TP + FP )( TP + FN )( TN + FP )( TN + FN )

For each similarity measure, the higher the value, the better the segmentation. Except for MCC , all of these measures have a unit range, that is, [0 ; 1]. The [ GLYPH&lt;0&gt; 1 1] range of ; MCC can be mapped to [0 1] by adding one to it and then dividing by two. Each of ; these unit-range similarity measures can then be converted to a unit-range dissimilarity measure by subtracting it from one. Note that there are also dissimilarity measures with no corresponding similarity formulation. A prime example is the well-known XOR measure (Hance et al., 1996) defined as follows:

$$X O R = \frac { | A S \oplus M S | } { | M S | } = \frac { | ( A S \cup M S ) - ( A S \cap M S ) | } { | M S | } = \frac { F P + F N } { T P + F N }.$$

It is essential to notice that di GLYPH&lt;11&gt; erent evaluation measures capture di GLYPH&lt;11&gt; erent aspects of a segmentation algorithm's performance on a given dataset, and thus there is no universally applicable evaluation measure (Japkowicz and Shah, 2011). This is why most studies employ multiple evaluation measures in an e GLYPH&lt;11&gt; ort to perform a comprehensive performance evaluation. Such a strategy, however, complicates algorithm comparisons, unless one algorithm completely dominates the others with respect to all adopted evaluation measures.

Based on their observation that experts tend to avoid missing parts of the lesion in their manual borders, Garnavi et al. (2011a) argue that true positives have the highest importance in the segmentation of skin lesion images. The authors also assert that false positives (background pixels incorrectly identified as part of the lesion) are less important than false negatives (lesion pixels incorrectly identified as part of the background). Accordingly, they assign a weight of 1 5 to : TP to signify its overall importance. Furthermore, in measures that involve both FN and FP (e.g., AC F , , and XOR ), they assign a weight of 0.5 to FP to emphasize its importance over FN . Using these weights, they construct a weighted performance index , which is an arithmetic average of six commonly used measures, namely SE SP PR AC F , , , , , and (unit complement of) XOR . This scalar evaluation measure facilitates comparisons among algorithms.

In a follow-up study, Garnavi and Aldeen (2011) parameterize the weights of TP FN FP , , , and TN in their weighted performance index and then use a constrained non-linear program to determine the optimal weights. They conduct experiments with five segmentation algorithms on 55 dermoscopic images. They conclude that the optimized weights not only lead to automated algorithms that are more accurate against manual segmentations, but also diminish the di GLYPH&lt;11&gt; erences among those algorithms.

We make the following key observations about the popular evaluation metrics and how they have been used in the skin lesion segmentation literature:

<!-- page_break -->

- GLYPH&lt;136&gt; Historically, AC has been the most popular evaluation measure owing to its simple and intuitive formulation. However, this measure tends to favor the majority class, leading to overly optimistic performance estimates in class-imbalanced domains. This drawback prompted the development of more elaborate performance evaluation measures, including GM BA , , and MCC .
- GLYPH&lt;136&gt; SE and SP are especially popular in medical domains, tracing their usage in serologic test reports in the early 1900s (Binney et al., 2021). SE (aka True Positive Rate ) quantifies the accuracy on the positive class, whereas SP (aka True Negative Rate ) quantifies the accuracy on the negative class. These measures are generally used together because it is otherwise trivial to maximize one at the expense of the other (an automated border enclosing the corresponding manual border will attain a perfect SE , whereas in the opposite case, we will have a perfect SP ). Unlike AC , they are suitable for class-imbalanced domains. BA and GM combine these measures into a single evaluation measure through arithmetic and geometric averaging, respectively. Unlike AC , these composite measures are suitable for class-imbalanced domains (Luque et al., 2020).
- GLYPH&lt;136&gt; PR is the proportion of examples assigned to the positive class that actually belongs to the positive class. RE is equivalent to SE PR . and RE are typically used in information retrieval applications, where the focus is solely on relevant documents (positive class). F combines these measures into a single evaluation measure through harmonic averaging. This composite measure, however, is unsuitable for class-imbalanced domains (Zou et al., 2004; Chicco and Jurman, 2020; Luque et al., 2020).
- GLYPH&lt;136&gt; MCC is equivalent to the phi coe GLYPH&lt;14&gt; cient , which is simply the Pearson correlation coe GLYPH&lt;14&gt; cient applied to binary data (Chicco and Jurman, 2020). MCC values fall within the range of [ GLYPH&lt;0&gt; 1 1] with ; GLYPH&lt;0&gt; 1 and 1 indicating perfect misclassification and perfect classification, respectively, while 0 indicating a classification no better than random (Matthews, 1975). Although it is biased to a certain extent (Luque et al., 2020; Zhu, 2020), this measure appears to be suitable for class-imbalanced domains (Boughorbel et al., 2017; Chicco and Jurman, 2020; Luque et al., 2020).
- GLYPH&lt;136&gt; J (aka Intersection over Union (Jaccard, 1912)) and F (aka Dice coe GLYPH&lt;14&gt; cient aka Sørensen-Dice coe GLYPH&lt;14&gt; cient (Dice, 1945; Sørensen, 1948)) are highly popular in medical image segmentation (Crum et al., 2006). These measures are monotonically related as follows: J = F = (2 GLYPH&lt;0&gt; F ) and F = 2 J = (1 + J ). Thus, it makes little sense to use them together. There are two major di GLYPH&lt;11&gt; erences between these measures: (i) (1 GLYPH&lt;0&gt; J ) is a proper distance metric, whereas (1 GLYPH&lt;0&gt; F ) is not (it violates the triangle inequality). (ii) It can be shown (Zijdenbos et al., 1994) that if TN is su GLYPH&lt;14&gt; ciently large compared to TP FN , , and FP , which is common in skin lesion segmentation, F becomes equivalent to Cohen's kappa (Cohen, 1960), which is a chance-corrected measure of inter-observer agreement.
- GLYPH&lt;136&gt; Among the seven composite evaluation measures given above, AC GM BA , , , and MCC are symmetric, that is, they are invariant to class swapping, while F J , , and XOR are asymmetric.
- GLYPH&lt;136&gt; XOR is similar to False Negative Rate , that is, the unit complement of SE , with the exception that XOR has an extra additive TN term in its numerator. While XOR values are guaranteed to be nonnegative, they do not have a fixed upper bound, which makes aggregations of this measure di GLYPH&lt;14&gt; cult. XOR is also biased against small lesions (Celebi et al., 2009c). Nevertheless, owing to its intuitive formulation, XOR was popular in skin lesion segmentation until about 2015 (Celebi et al., 2015b).

<!-- page_break -->

- GLYPH&lt;136&gt; The 2016 and 2017 ISIC Challenges (Gutman et al., 2016; Codella et al., 2018) adopted five measures: AC SE SP F , , , , and J , with the participants ranked based on the last measure. The 2018 ISIC Challenge (Codella et al., 2019) featured a thresholded Jaccard index , which returns the same value as the original J if the value is greater than or equal to a predefined threshold and zero otherwise. Essentially, this modified index considers automated segmentations yielding J values below the threshold as complete failures. The challenge organizers set the threshold equal to 0 65 based on an earlier study (Codella et al., 2017) : that determined the average pairwise J similarities among the manual segmentations outlined by three expert dermatologists. Since the majority of papers in this survey (168 out of 177 papers) use the ISIC datasets (Fig. 3), we list the J for all the papers in Table 3 wherever it has been reported in the corresponding papers. For papers that did not report J and instead reported F , we list the computed J based on F and denote it with an asterisk.
- GLYPH&lt;136&gt; Some of the aforementioned measures (i.e., GM and BA ) have not been used in a skin lesion segmentation study yet.
- GLYPH&lt;136&gt; The evaluation measures discussed above are all region-based and thus fairly insensitive to border irregularities (Lee et al., 2003), i.e., indentations, and protrusions along the border. Boundary-based evaluation measures (Taha and Hanbury, 2015) have not been used in the skin lesion segmentation literature much except for the symmetric Hausdor GLYPH&lt;11&gt; metric (Silveira et al., 2009), which is known to be sensitive to noise (Huttenlocher et al., 1993) and biased in favor of small lesions (Bogo et al., 2015).