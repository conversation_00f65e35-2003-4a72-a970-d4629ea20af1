## Training and Validation Accuracy Accuracy

Figure 7. Training and validation accuracy of the best model.

picture_counter_24 The image shows a line plot depicting training and validation accuracy over epochs. The x-axis represents the epoch, and the y-axis represents the accuracy. The plot includes two lines: one for the training accuracy (blue) and one for the validation accuracy (orange).

Vol.:(0123456789)

<!-- page_break -->

Vol:.(1234567890)


## Training and Validation Losses

picture_counter_25 The image is a line plot showing training and validation losses over epochs. The x-axis represents the epoch, and the y-axis represents the loss.  A blue line represents the 'train' loss, and an orange line represents the 'val_accuracy'.

Figure 8. Training and validation losses of the best model.

Figure 9. Training and test ROC curve of the best achieved model.

picture_counter_26 The image is a Receiver Operating Characteristic (ROC) curve. The x-axis represents the false positive rate, and the y-axis represents the true positive rate. There are two curves: one blue, labeled "(Train = 0.99)", and one orange, labeled "(Test = 0.95)". A black dashed diagonal line is also present.

Figure10. Confusion matrix of the best model.

picture_counter_27 The image is a confusion matrix. The rows represent the actual classes (Normal and COVID-19), and the columns represent the predicted classes (Normal and COVID-19). The matrix displays the counts of true positives, true negatives, false positives, and false negatives.

<!-- page_break -->

Table 6. Comparison of proposed method with the relevant researches.

| Ref            | Techniques         | Modalities          | Task                                                                          | Accuracy                                                             |
|----------------|--------------------|---------------------|-------------------------------------------------------------------------------|----------------------------------------------------------------------|
| 19             | CNN(VGG16)         | 1248 chest X-ray    | 3-classes classification: (Healthy/COVID- 19pneumonia/non-COVID-19 pneumonia) | 83.6% accuracy, 90.9% sensitivity                                    |
| 20             | SqueezeNet         | 5184 Chest X-rays   | Binary classification of images as COVID-19 or not                            | 98% sensitivity, 92.9 specificity                                    |
| 21             | CNNmodel           | 13,824 X-ray images | Binary classification of images as COVID-19 or normal                         | 96.71% accuracy, 97% F1-score                                        |
| 22             | CNNcalled COVID-Ne | 13,975X-ray         | Prediction of cases as (normal/pneumonia/COVID- 19)                           | 93.3% accuracy                                                       |
| 23             | CNNcalled nCOVnet  | 284 X-ray images    | (Normal/COVID-19)                                                             | 88.1% accuracy                                                       |
| 24             | EfficientNet       | 16,634 X-ray images | Classify cases as: (Normal/COVID-19/other)                                    | 93.48% accuracy                                                      |
| 25             | VGG-16             | 700 X-ray images    | binary classification (Normal/COVID-19)                                       | 94.3% accuracy, 94.3% F1-score                                       |
| Proposed model | VGG19              | 1600 X-ray images   | Binary classification (Normal/COVID-19)                                       | 95% accuracy, 0.9505% F1-score, 0.96% sensitivity, 0.94% specificity |

Table 7. The results of best achieved models for 4-classes classification.

| Dataset    | Model          |   F1 score |   Test acc |
|------------|----------------|------------|------------|
| Histeq     | EfficientNetB0 |   0.93495  |    0.935   |
| CLAHE      | VGG19          |   0.9339   |    0.93375 |
| Complement | VGG19          |   0.933786 |    0.93375 |

datasets used different number of samples. Some research performed binary class classification, where others performed multi-class classification. Thus, the proposed work has been compared with others that used the same modality which is X-ray with mentioning the number of used samples and the task.

By comparing the results of the proposed framework with recent literature, it was found that the proposed framework outperforms most of the state-of-the-art works. However, 21 slightly outperforms the proposed framework. Where the accuracy and F1-score   of 21 are 96.71% and 97% respectively, the corresponding values of the proposed framework are 95% and 0.9505 respectively. Taking in consideration that   in 21 un-balanced data has been used; the number of COVID images used is 3626 where the number of normal images is 10,198 as mentioned in the manuscript.

For more validation, different classes of the datasets have been used for training the CNN models. To check the capability of the proposed framework for 4-classes classification. The dataset versions that achieved the highest binary classification have been utilized for multi-classes classification. Since the performance of all models that trained using enhanced full image versions is close to each other, therefore, these versions have been utilized for 4-classes classification. A set of 3200 X-ray images (800 of each class) have been used to train CNN models. The newly added classes are Viral Pneumonia and Lung Opacity in addition to COVID-19 and normal classes.

It was found that the best-achieved accuracy of 4-classes classification using the full image versions reached 0.935 for histeq version by EfficientNetB0. While it reached 0.93375 for both CLAHE, and complement versions using VGG19. Table 7 shows the results of the best-achieved models for 4-classes classification.