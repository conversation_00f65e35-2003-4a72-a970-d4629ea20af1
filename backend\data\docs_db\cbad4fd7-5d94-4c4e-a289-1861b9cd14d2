## Multi-layer CNN

First, we need to determine the architecture of our model. The input form of our data is 400 × 400 and has 3 channels. Since we have a total of 4 different classes, the number of output classes is set to 4. Our model has a structure that includes convolutional and pooling layers. First, there is a 3 × 3 convolutional layer with 32 filters. This is followed by a 2 × 2 max pooling layer. This reduces the size by emphasizing lower-level features. To deepen our model, this structure is repeated twice, adding convolutional layers with 64 and 128 filters, respectively, and maximum pooling layers of size 2 × 2.

Table 2. Confusion matrix.

picture_counter_2 The image presents a confusion matrix with the following elements:

*   **Rows:** Represent the estimated values (positive and negative).
*   **Columns:** Represent the actual values (positive and negative).
*   **Cells:** Contain abbreviations for true positives (Tp), false positives (Fp), false negatives (Fn), and true negatives (Tn).
*   **Totals:** Include TPos, TNeg, Pos, Neg, and M.

|                | Actual value   | Actual value   | Actual value   |
|----------------|----------------|----------------|----------------|
|                | Positive       | Negative       | Total          |
| Estimate value | Estimate value | Estimate value | Estimate value |
| Positive       | T p            | F p            | TPos           |
| Negative       | F N            | T N            | TNeg           |
| Total          | Pos            | Neg            | M              |

<!-- page_break -->

The resulting feature map is transformed into a flat vector with a flattening layer. A hidden (dense) layer of 128 neurons is then added. This layer deepens the learned features and increases generalization. Finally, the output layer has 4 neurons and calculates the probabilities between classes with the softmax activation function. To train our model, we need to determine the optimal function and metrics. In this paper, we use the Rectified Adam optimization algorithm. This algorithm dynamically adjusts the learning rate and helps to use gradients more efficiently. Also, categorical cross-entropy is used as the loss function during training, as it is widely used in multiclass classification task.

The metrics tracked during training are accuracy, as well as precision and recall. These metrics are important for evaluating the classification performance of the model. In addition, a reduced learning rate recall (ReduceLROnPlateau) is used to dynamically adjust the learning rate. This recall reduces the learning rate when the loss function flattens out during the training process, resulting in more stable training. The epoch is set to 14 and the batch size to 10.


## CNN-based transfer learning

In transfer learning architectures, all parameters and layers outside the model are the same, but after the last 3 layers of transfer learning models are removed, layers unique to the dataset are added instead: the GlobalAveragePooling2D layer contains fewer parameters than the Flatten layer, which reduces the risk of overfitting and helps build a more efficient model. Also, while the Flatten layer is used to organize the data, the GlobalAveragePooling2D layer is used for feature extraction, making the network learning process more efficient.

Due to the fact that the training data tends to learn very fast compared to the validation data, we modified the ratio of the dropout layers in the original architectures. For all models, the dilution rate was set to 0.05. During the model training process, the designated optimizer was "RectifiedAdam", with the optimizer parameters configured as follows: learning\_rate = 0.0001, beta\_1 = 0.9, beta\_2 = 0.999, and epsilon = 1e-08. The loss function selected is categorical\_crossentropy, while the metrics used include precision, recall, categorical accuracy, and accuracy. This completes the pre-training of the model. The final layer of the model is the dense layer, which contains 4 neurons, which is usually the number of output classes in classification problems. The activation function of this layer is "softmax". The softmax function makes the output values interpretable as probabilities between classes. Furthermore, the data type of this layer is "float64", which means that the output values are of a 64-bit double precision type. The layer also applies regularization using the "kernel\_regularizer" property. The L2 regularization used here aims to reduce the risk of overfitting by limiting the size of the weights. The regularization coefficient of 0.1, denoted by "regulars. l2 (0.1)", controls the effectiveness of the regularization.

During the model training process, the "ReduceLROnPlateau" function of the Keras library was used as a backpropagation algorithm. This function automatically reduced the learning rate when the model approached a local optimum or when the loss value did not decrease. The parameters of the "ReduceLROnPlateau" function are as follows monitor: The metric monitored is usually "val\_loss" (validation loss). This is the metric used to determine if the learning rate should be reduced:

- · patience: The expected patience time for lowering the learning rate, i.e. how long the metric should not improve.
- · factor: The factor used to reduce the learning rate. For example, a value of 0.3 reduces the learning rate by 30%.
- · min\_lr: Specifies the minimum achievable learning rate. This limits the learning rate without making it infinitesimal.

Using this feature allows for more stable and efficient model training, streamlining the process of fine-tuning training parameters without the need to manually adjust the learning rate. The training program was run over 14 epochs with batches of size 10. Details of the multilayer CNN model used in the study are presented in Fig. 2, which outlines its architectural features.

The training and validation accuracy loss graphs of the models created with VGG19, EfficientNetB4, InceptionV3 transfer learning, and CNN are shown in Fig. 3.

Table 3 shows the accuracy, F-score, Recall, Precision and AUC results of the models created in the study.

Figure 2. Multi-layer CNN model arthitecture.

picture_counter_3 The image depicts a convolutional neural network (CNN) architecture for medical image analysis. It starts with a brain MRI image, followed by convolutional layers with max pooling. The final layer is a fully connected layer, leading to outputs for Glioma, Meningioma, No Tumor, and Pituitary diagnoses.

Vol.:(**********)

<!-- page_break -->

Vol:.(**********)

Figure 3. Learning curves of losses and accuracies of ( a ) CNN model, ( b ) EfficientNetB4 model, ( c ) VGG19 model, ( d ) InceptionV3, ( e ) VGG16 model.

picture_counter_4 The image presents five sets of graphs. Each set includes two plots: "Training and Validation Accuracy" and "Training and Validation Loss". Each set represents a different model: (a) CNN, (b) EfficientNetB4, (c) VGG19, (d) InceptionV3, and (e) VGG16. The x-axis of each graph represents "Epochs," ranging from 1 to 14. The y-axis of the "Training and Validation Accuracy" graphs represents "Accuracy," while the y-axis of the "Training and Validation Loss" graphs represents "Loss." Each graph shows the training and validation accuracy/loss curves.

Table 3. Performances (%) of the models created in the study.

| Models         |   Accuracy |   F-score |   Recall |   Precision |   AUC |
|----------------|------------|-----------|----------|-------------|-------|
| VGG19          |         96 |        96 |       96 |          96 |    99 |
| EfficientNETB4 |         97 |        96 |       97 |          97 |    99 |
| InceptionV3    |         96 |        96 |       96 |          96 |    99 |
| 3 CNNModel     |         91 |        90 |       91 |          91 |    98 |
| VGG16          |         98 |        97 |       98 |          98 |    99 |

According to Table 3, the best accuracy result was obtained by VGG16 with 97%. It is ahead of other methods with F-score value of 97%, AUC value of 99%, recall value of 98% and precision values of 98%. The ROC curves of the models created in the study are shown in Fig. 4.

According to the AUC values in Fig. 4, the transfer learning models VGG, InceptionV3, and EfficientNetB4 and the models built with CNN have distinctive features. The confusion matrix of the study on the classification of glioma, meningioma, non-tumor normal patients, pituitary tumor patients in the dataset by tumor type is shown in Fig. 5.

As shown in the confusion matrix in Fig. 5, the classification performance is high for all four models (VGG16 and VGG19 models, CNN model, EfficientNetB4 model, InceptionV3 model).


## Results and discussion

As part of the study, CNN and CNN-based transfer learning models such as InceptionV3, EfficientNetB4, VGG19 were trained on open-source shared brain tumor patients. The best accuracy result was obtained with EfficientNetB4 with 95%. The comparison of the brain tumor studies with the literature is shown in Table 4.

As shown in Table 4, the CNN-based transfer learning models used in the study performed better. AI in healthcare plays an important role in the management of complex diseases such as brain tumors. AI enables faster, more accurate, and more effective diagnosis and treatment processes. However, AI technology is not intended to completely replace doctors, but to support and enhance their work. To realize the full potential of AI, it is important to consider issues such as ethics, security and privacy. In the future, AI-based solutions will continue to contribute to better management of brain tumors and other health problems, and improve the quality of life

<!-- page_break -->

Figure 4. The ROC curve of ( a ) CNN model, ( b ) EfficientNetB4 model, ( c ) VGG19 model ( d ) InceptionV3 model, ( e ) VGG16.

picture_counter_5 The image presents five Receiver Operating Characteristic (ROC) curves, each labeled with a different model: (a) CNN, (b) EfficientNetB4, (c) VGG19, (d) InceptionV3, and (e) VGG16. Each ROC curve plots True Positive Rate against False Positive Rate, with the Area Under the Curve (AUC) provided for each of the four categories: glioma tumor, meningioma tumor, no tumor, and pituitary tumor. The curves are color-coded, with a legend specifying the color for each category.

for patients. As seen in this study, AI-based studies will increase their importance to human health, from early diagnosis to positive progress in the treatment process.

Based on the results of this study, transfer learning methods should be preferred especially in image processing-based applications to support health decision makers. The data obtained from MRI or CT can be used as an early warning system to help health decision makers make quick and accurate decisions. Therefore, in addition to empirical analysis, AI-based applications should take a more active role as soon as possible. To this end, the diagnosis of diseases from instant CT or MR images will be investigated in the coming years.


## Limitation

With our motivation to investigate how it will work in single CNN and multilayer CNN based transfer learning models, we subjected the dataset to classification as it is without rotation and cropping operations, which is the most important limitation of our study.

Vol.:(**********)

<!-- page_break -->

Vol:.(**********)

Figure 5. The confusion metrics of ( a ) CNN model, ( b ) EfficientNetB4 model, ( c ) VGG19 model ( d ) InceptionV3 model, ( e ) VGG16.

picture_counter_6 The image contains five confusion matrices (a) CNN, (b) EfficientNetB4, (c) VGG19, (d) InceptionV3, and (e) VGG16. Each matrix represents the performance of a different AI model in classifying four types of tumors: glioma tumor, meningioma tumor, no tumor, and pituitary tumor. The matrices show the number of correctly and incorrectly classified instances for each tumor type.

Table 4. Comparison with previous studies on brain tumor. Best results obtained in the study.

| Authors                 | Dataset                          | Models                                     | Accuracy (%)                        |
|-------------------------|----------------------------------|--------------------------------------------|-------------------------------------|
| Wallis and Buvat 1      | Brain Tumor Dataset 28           | SVM                                        | 74                                  |
| Seere and Karibasappa 2 | Their own Brain Tumor dataset    | SVM                                        | 85.32                               |
| Ortiz-Ramón et al. 3    | Their own Brain Tumor dataset    | SVM                                        | 89.6                                |
| Gupta and Sasidhar 4    | MICCAI 2012 Challenge database 4 | SVM                                        | 87                                  |
| Gumaei et al. 5         | Brain Tumor Dataset 28           | RELM                                       | 92.61                               |
| Shahajad et al. 6       | Kaggle brain dataset 17          | SVM                                        | 92                                  |
| Vankdothu et al. 7      | Brain Tumor Dataset 17           | CNN, LSTM CNN-LSTM                         | CNN89 LSTM 90.02 CNN-LSTM 92        |
| Sirinivas et al. 8      | Brain Tumor Dataset 17           | InceptionV3 VGG16 ResNET50                 | InceptionV3 78 VGG16 96 ResNET50 95 |
| Choudhury et al. 9      | Their own Brain Tumor dataset    | CNN                                        | 96.08                               |
| Martini and Oermann 10  | Their own Brain Tumor dataset    | CNN                                        | 93.09                               |
| Sarkar et al. 11        | Their own Brain Tumor dataset    | CNN                                        | 91.03                               |
| Arunkumar et al. 12     | None                             | SVM, KNN                                   | 92.14                               |
| Zacharaki et al. 13     | Their own Brain Tumor dataset    | SVM, KNN                                   | 88                                  |
| Cheng et al. 14         | Their own Brain Tumor dataset    | SVM, KNN                                   | 91.28                               |
| Paul et al. 15          | Their own Brain Tumor dataset    | CNN                                        | 91.43                               |
| Afshar et al. 16        | Brain Tumor Dataset 28           | CapsNet                                    | 90.89                               |
| This study              | Brain Tumor Dataset 17           | EfficientNetB4 InceptionV3 VGG19 VGG16 CNN | 97 95 96 98 91                      |

<!-- page_break -->