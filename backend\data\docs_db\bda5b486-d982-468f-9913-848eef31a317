## 2. Input Data

Obtaining data in su GLYPH&lt;14&gt; cient quantity and quality is often a significant obstacle to developing e GLYPH&lt;11&gt; ective segmentation models. State-of-the-art segmentation models have a huge number of adjustable parameters that allow them to generalize well, provided they are trained on massive labeled datasets (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2020). Unfortunately, skin lesion datasets-like most medical image datasets (<PERSON><PERSON><PERSON> et al., 2021)-tend to be small (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019) due to issues such as copyright, patient privacy, acquisition and annotation cost, standardization, and scarcity of many pathologies of interest. The two most common modalities used in the training of skin lesion segmentation models are clinical images , which are close-ups of the lesions acquired using conventional cameras, and dermoscopic images , which are acquired using dermoscopy, a non-invasive skin imaging through optical magnification, and either liquid immersion and low angle-of-incidence lighting, or cross-polarized lighting. Dermoscopy eliminates skin surface reflections (<PERSON><PERSON> et al., 2002), reveals subsurface skin structures, and allows the identification of dozens of morphological features such as atypical pigment networks, dots globules, streaks, blue-white areas, and / blotches (<PERSON><PERSON><PERSON> et al., 2003).

Annotation is often the greatest barrier for increasing the amount of data. Objective evaluation of segmentation often requires laborious region-based annotation , in which an expert manually outlines the region where the lesion (or a clinical feature) appears in

3 Arxiv Sanity Preserver : https://www.arxiv-sanity-lite.com/search?q=segmentation+skin+melanoma+deep+learning+convolution+

<!-- page_break -->

the image. By contrast, textual annotation may involve diagnosis (e.g., melanoma, carcinoma, benign nevi), presence absence score / / of dermoscopic features (e.g., pigment networks, blue-white areas, streaks, globules), diagnostic strategy (e.g., pattern analysis, ABCD rule, 7-point checklist, 3-point checklist), clinical metadata (e.g., sex, age, anatomic site, familial history), and other details (e.g., timestamp, camera model) (Ca GLYPH&lt;11&gt; ery et al., 2018). We extensively discuss the image annotation issue in Section 4.1.


## 2.1. Datasets

The availability of larger, more diverse, and better-annotated datasets is one of the main driving factors for the advances in skin image analysis in the past decade (Marchetti et al., 2018; Celebi et al., 2019). Works in skin image analysis date back to the 1980s (Vanker and Van Stoecker, 1984; Dhawan et al., 1984), but until the mid-2000s, these works used small, private datasets, containing a few hundred images.

The Interactive Atlas of Dermoscopy (sometimes called the Edra Atlas , in reference to the publisher) by Argenziano et al. (2000) included a CD-ROM with 1 039 dermoscopy images (26% melanomas, 4% carcinomas, 70% nevi) of 1 024 ; ; GLYPH&lt;2&gt; 683 pixels, acquired by three European university hospitals (University of Graz, Austria, University of Naples, Italy, and University of Florence, Italy). The works of Celebi et al. (2007b, 2008) popularized the dataset in the dermoscopy image analysis community, where it became a de facto evaluation standard for almost a decade, until the much larger ISIC Archive datasets (see below) became available. Recently, Kawahara et al. (2019) placed this valuable dataset, along with additional textual annotations based on the 7-point checklist, in public domain under the name derm7pt . Shortly after the publication of the Interactive Atlas of Dermoscopy, Menzies et al. (2003) published An Atlas of Surface Microscopy of Pigmented Skin Lesions: Dermoscopy , with a CD-ROM containing 217 dermoscopic images (39% melanomas, 7% carcinomas, 54% nevi) of 712 GLYPH&lt;2&gt; 454 pixels, acquired at the Sydney Melanoma Unit, Australia.

The PH 2 dataset, released by Mendonca et al. (2013) and detailed by Mendonca et al. (2015), was the first public dataset to provide region-based annotations with segmentation masks, and masks for the clinically significant colors (white, red, light brown, dark brown, blue-gray, and black) present in the images. The dataset contains 200 dermoscopic images (20% melanomas, 40% atypical nevi, and 40% common nevi) of 768 GLYPH&lt;2&gt; 560 pixels, acquired at the Hospital Pedro Hispano, Portugal. The Edinburgh DermoFit Image Library (Ballerini et al., 2013) also provides region-based annotations for 1 ; 300 clinical images (10 diagnostic categories including melanomas, seborrhoeic keratosis, and basal cell carcinoma) of sizes ranging from 177 GLYPH&lt;2&gt; 189 to 2 ; 176 GLYPH&lt;2&gt; 2 549 ; pixels. The images were acquired with a Canon EOS 350D SLR camera, in controlled lighting and at a consistent distance from the lesions, resulting in a level of quality atypical for clinical images.

The ISIC Archive contains the world's largest curated repository of dermoscopic images. ISIC , an international academiaindustry partnership sponsored by ISDIS (International Society for Digital Imaging of the Skin), aims to 'facilitate the application of digital skin imaging to help reduce melanoma mortality' (ISIC, 2023). At the time of writing, the archive contains more than 240 000 images, of which more than 71 000 are publicly available. ; ; These images were acquired in leading worldwide clinical centers, using a variety of devices.

In addition to curating the datasets that collectively form the ' ISIC Archive', ISIC has released standard archive subsets as part of its Skin Lesion Analysis Towards Melanoma Detection Challenge, organized annually since 2016. The 2016, 2017, and 2018 challenges comprised segmentation, feature extraction, and classification tasks, while the 2019 and 2020 challenges featured

<!-- page_break -->

Table 1: Public skin lesion datasets with lesion segmentation annotations. All the datasets contain RGB images of skin lesions.

| dataset                                                                          |   year | modality   | size     | training / validation / test   | class distribution                                                                  | additional info                                                                                      |
|----------------------------------------------------------------------------------|--------|------------|----------|--------------------------------|-------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|
| DermQuest 4 (DermQuest, 2012)                                                    |   2012 | clinical   | 137      | -                              | 61 non-melanomas 76 melanomas                                                       | acquired with di GLYPH<11> erent cameras under various lighting conditions                           |
| DermoFit (Ballerini et al., 2013)                                                |   2013 | clinical   | 1 ; 300  | -                              | 1 ; 224 non-melanomas 76 melanomas                                                  | sizes ranging from 177 GLYPH<2> 189 to 2 ; 176 GLYPH<2> 2 ; 549 pixels                               |
| Pedro Hispano Hospital (PH 2 ) (Mendonca et al., 2013)                           |   2013 | dermoscopy | 200      | -                              | 160 benign nevi 40 melanomas                                                        | sizes ranging from 553 GLYPH<2> 763 to 577 GLYPH<2> 769 pixels acquired at 20 GLYPH<2> magnification |
| ISIC2016 (Gutman et al., 2016)                                                   |   2016 | dermoscopy | 1 ; 279  | 900 / - / 379                  | Training: 727 non-melanomas 173 melanomas Test: 304 non-melanomas 75 melanomas      | sizes ranging from 566 GLYPH<2> 679 to 2 ; 848 GLYPH<2> 4 ; 288 pixels                               |
| ISIC2017 (Codella et al., 2018)                                                  |   2017 | dermoscopy | 2 ; 750  | 2 ; 000 / 150 / 600            | Training: 1 ; 626 non-melanomas 374 melanomas Test: 483 non-melanomas 117 melanomas | sizes ranging from 540 GLYPH<2> 722 to 4 ; 499 GLYPH<2> 6 ; 748 pixels                               |
| ISIC2018 (Codella et al., 2019)                                                  |   2018 | dermoscopy | 3 ; 694  | 2 ; 594 / 100 / 1 ; 000        | -                                                                                   | sizes ranging from 540 GLYPH<2> 576 to 4 ; 499 GLYPH<2> 6 ; 748 pixels                               |
| HAM10000 (Tschandl et al., 2018) (Tschandl et al., 2020) (ViDIR Dataverse, 2020) |   2020 | dermoscopy | 10 ; 015 | -                              | 1 ; 113 non-melanomas 8 ; 902 melanomas                                             | all images of 600 GLYPH<2> 450 pixels                                                                |

only classification. Each subset is associated with a challenge (year), one or more tasks, and has two (training test) or three / (training validation test) splits. / / The ISIC Challenge 2016 (Gutman et al., 2016) ( ISIC 2016, for brevity) contains 1 ; 279 images split into 900 for training (19% melanomas, 81% nevi), and 379 for testing (20% melanomas, 80% nevi). There is a large variation in image size, ranging from 0 5 to 12 megapixels. : All tasks used the same images. The ISIC 2017 (Codella et al., 2018) dataset more than doubled, with 2 750 images split into 2 ; ; 000 for training (18 7% melanomas, 12 7% seborrheic keratoses, 68 6% nevi), : : : 150 for validation (20% melanomas, 28% seborrheic keratoses, 52% nevi), and 600 for testing (19 5% melanomas, 15% seborrheic : keratoses, 65 5% nevi). Again, image size varied markedly, ranging from 0 5 to 29 megapixels, and all tasks used the same images. : :

ISIC 2018 provided, for the first time, separate datasets for the tasks, with 2 ; 594 training (20% melanomas, 72% nevi, and 8% seborrheic keratoses) and 100 1 / ; 000 for validation test images ranging from 0 5 to 29 megapixels, for the tasks of segmentation and / : feature extraction (Codella et al., 2019), and 10 ; 015 1 512 training test images for the classification task, all with 600 / ; / GLYPH&lt;2&gt; 450 pixels. The training dataset for classification was the HAM10000 dataset (Tschandl et al., 2018), acquired over a period of 20 years at the Medical University of Vienna, Austria and the private practice of Dr. Cli GLYPH&lt;11&gt; Rosendahl, Australia. It allowed a five-fold increase in training images in comparison to 2017 and comprised seven diagnostic categories: melanoma (11 1%), nevus (66 9%), basal : : cell carcinoma (5 1%), actinic keratosis or Bowen's disease (3 3%), benign keratosis (solar lentigo, seborrheic keratosis, or lichen : : planus-like keratosis, 11%), dermatofibroma (1 1%), and vascular lesion (1 4%). : : As a part of a 2020 study of human-computer collaboration for skin lesion diagnosis involving dermatologists and general practitioners (Tschandl et al., 2020), the lesions in the HAM10000 dataset were segmented by a single dermatologist and consequently released publicly (ViDIR Dataverse, 2020), making this the single largest publicly available skin lesion segmentation dataset (Table 1).

<!-- page_break -->

ISIC 2019 (Codella et al., 2018; Tschandl et al., 2018; Combalia et al., 2019) contains 25 ; 331 training images (18% melanomas, 51% nevi, 13% basal cell carcinomas, 3 5% actinic keratoses, 10% benign keratoses, 1% dermatofibromas, 1% vascular lesions, : and 2 5% squamous cell carcinomas) and 8 238 test images (diagnostic distribution unknown). The images range from 600 : ; GLYPH&lt;2&gt; 450 to 1 ; 024 GLYPH&lt;2&gt; 1 024 pixels. ;

ISIC 2020 (Rotemberg et al., 2021) contains 33 126 training images (1 8% melanomas, 97 6% nevi, 0 4% seborrheic ker-; : : : atoses, 0 1% lentigines simplex, 0 1% lichenoid keratoses, 0 02% solar lentigines, 0 003% cafe-au-lait macules, 0 003% atypical : : : : : melanocytic proliferations) and 10 ; 982 test images (diagnostic distribution unknown), ranging from 0.5 to 24 megapixels. Multiple centers, distributed worldwide, contributed to the dataset, including the Memorial Sloan Kettering Cancer Center ( USA ), the Melanoma Institute, the Sydney Melanoma Diagnostic Centre, and the University of Queensland (Australia), the Medical University of Vienna (Austria), the University of Athens (Greece), and the Hospital Clinic Barcelona (Spain). An important novelty in this dataset is the presence of multiple lesions per patient, with the express motivation of exploiting intra- and inter-patient lesion patterns, e.g., the so-called 'ugly-ducklings', lesions whose appearances are atypical for a given patient, and which present an increased risk of malignancy (Gachon et al., 2005).

There is, however, an overlap among these ISIC Challenge datasets. Abhishek (2020) analyzed all the lesion segmentation datasets from the ISIC Challenges (2016-2018) and found considerable overlap between these 3 datasets, with as many as 1 940 ; images shared between at least 2 datasets and 706 images shared between all 3 datasets. In a more recent analysis of the ISIC Challenge datasets for the lesion diagnosis task from 2016 through 2020, Cassidy et al. (2022) found overlap between the datasets as well as the presence of duplicates within the datasets. Using a duplicate removal strategy, they curated a new set of 45 ; 590 training images (8 61% melanomas, 91 39% others) and 11 397 validation images (8 61% melanomas, 91 39% others), leading : : ; : : to a total of 56 ; 987 images. Additionally, since the resulting dataset is highly imbalanced (melanomas versus others in a ratio of 1 : 10 62), the authors also curated a balanced dataset with 7 : ; 848 training images (50% melanoma, 50% others) and 1 962 ; validation images (50% melanoma, 50% others).

Table 1 shows a list of publicly available skin lesion datasets with pixel-wise annotations, image modality, sample size, original split sizes, and diagnostic distribution. Fig. 3 shows how frequently these datasets appear in the literature. It is also worth noting that several other skin lesion image datasets have not been described in our survey as they do not provide the corresponding skin lesion segmentation annotations. However, these datasets, including SD-198 (Sun et al., 2016), MED-NODE (Giotis et al., 2015), derm7pt (Kawahara et al., 2019), Interactive Dermatology Atlas (Usatine and Madden, 2013), Dermatology Information System (DermIS, 2012), DermWeb (Lui et al., 2009), DermNet New Zealand (Oakley et al., 1995), may still be relevant for skin lesion segmentation research (see Section 5).

Biases in computer vision datasets are a constant source of issues (Torralba and Efros, 2011), which is compounded in medical imaging due to the smaller number of samples, insu GLYPH&lt;14&gt; cient image resolution, lack of geographical or ethnic diversity, or statistics unrepresentative of clinical practice. All existing skin lesion datasets su GLYPH&lt;11&gt; er to a certain extent from one or more of the aforementioned issues, to which we add the specific issue of the availability and reliability of annotations. For lesion classification, many

4 DermQuest was deactivated on December 31, 2019. However, 137 of its images are publicly available (Glaister, 2013).

<!-- page_break -->

Fig. 3: The frequency of utilization of di GLYPH&lt;11&gt; erent skin lesion segmentation datasets in the surveyed studies. We found that 82 papers evaluated on more than 1 dataset, with 36 papers opting for cross-dataset evaluation ( CDE in Table 3). ISIC datasets ( ISIC 2016, ISIC 2017, ISIC 2018, and ISIC Archive) are used in the majority of papers, with 168 of 177 papers using at least one ISIC dataset and the ISIC 2017 dataset being the most popular (117 papers). The PH 2 dataset is the second most widely used (56 papers) following ISIC datasets.

picture_counter_12 The image is a pie chart displaying the distribution of different datasets. The labels and corresponding percentages are: DermoFit (2.4%), DermQuest (2.0%), PH2 (18.9%), ISIC 2016 (13.8%), ISIC 2018 (21.2%), and ISIC 2017 (39.4%).

samples lack the gold standard histopathological confirmation, and ground-truth segmentation, even when available, is inherently noisy (Section 4.2). The presence of artifacts (Fig. 1) may lead to spurious correlations, an issue that Bissoto et al. (2019) attempted to quantify for classification models.