## 3.1. Architecture

An ideal skin lesion segmentation algorithm is accurate, computationally inexpensive, invariant to noise and input transformations, requires little training data and is easy to implement and train. Unfortunately, no algorithm has, so far, been able to achieve these conflicting goals. DL -based segmentation tends towards accuracy and invariance at the cost of computation and training data. Ease of implementation is debatable: on the one hand, the algorithms often forgo cumbersome preprocessing, postprocessing, and feature engineering steps. On the other hand, tuning and optimizing them is often a painstaking task.

As shown in Fig. 6, we have classified the existing literature into single-network models, multiple-network models, hybridfeature models, and Transformer models. The first and second groups are somewhat self-descriptive, but notice that the latter is further divided into ensembles of models, multi-task methods (often performing simultaneous classification and segmentation), and GAN s. Hybrid-feature models combine DL with hand-crafted features. Transformer models, as the name suggests, employ

<!-- page_break -->

Fig. 6: Taxonomy of DL -based skin lesion segmentation model architectures.

picture_counter_20 The image is a diagram illustrating segmentation model architectures. It includes Single Network Models, Multiple Network Models, Transformer Models, and Hybrid Feature Models. Single Network Models branch into Conv. Modules, Shortcut Connections, Multi-scale Modules, Attention Modules, and Recurrent CNNs. Multiple Network Models branch into Ensembles, Multi-task Models, and GANs.

Transformers either with or without CNNs for segmentation, and have started being used for skin lesion segmentation only recently. We classified works according to their most relevant feature, but the architectural improvements discussed in Section 3.1.1 also appear in the models listed in the other sections. In Fig. 7, we show how frequently di GLYPH&lt;11&gt; erent architectural modules appear in the 177 surveyed works, grouped by our taxonomy of model architectures (Fig. 6).

Table 3 summarizes all the 177 surveyed works in this review, with the following attributes for each work: type of publication, datasets, architectural modules, loss functions, and augmentations used, reported Jaccard index, whether the paper performed cross-dataset evaluation ( CDE ) and postprocessing ( PP ), and whether the corresponding code was released publicly. For papers that reported segmentation results on more than 1 dataset, we list all of them and list the performance on only one dataset, formatting that particular dataset in bold. Since ISIC 2017 is the most popular dataset (Fig. 3), wherever reported, we note the performance (Jaccard index) on ISIC 2017. For papers that do not report the Jaccard index and instead report the Dice score, we compute the former based on the latter and report this computed score denoted by an asterisk. Cross-dataset evaluation ( CDE ) refers to when a paper trained model(s) on one dataset but evaluated on another..


## 3.1.1. Single Network Models

The approaches in this section employ a single DL model, usually an FCN , following an encoder-decoder structure, where the encoder extracts increasingly abstract features, and the decoder outputs the segmentation mask. In this section, we discuss these architectural choices for designing deep models for skin lesion segmentation.

Earlier DL -based skin lesion segmentation works adopted either FCN (Long et al., 2015) or U-Net (Ronneberger et al., 2015).

<!-- page_break -->

Fig. 7: The frequency of utilization of di GLYPH&lt;11&gt; erent architectural modules in the surveyed studies. Shortcut connections, particularly, skip connections (112 papers) and residual connections (70 papers) are the two most frequent components in DL -based skin lesion segmentation models. Attention mechanisms learn dependencies between elements in sequences, either spatially or channel-wise, and are therefore used by several encoder-decoder-style segmentation models (41 papers). Dilated convolutions help expand the receptive field of CNN -models without any additional parameters, which is why they are the most popular variant of convolution in the surveyed studies (35 papers). Finally, papers using Transformers (12 papers) started appearing from 2021 onwards and are on the rise.

picture_counter_21 The image is a stacked bar plot. The x-axis represents different modules or techniques: Shortcut Connections, Convolutional modules, Multi-scale Modules, GAN, Attention Modules, RCNN, and Transformer. The y-axis represents the number of occurrences, ranging from 0 to 200. Each bar is stacked with different colored sections, representing different components like dense connection, image pyramid, Transformer, recurrent CNN, attention module, GAN, pyramid pooling, etc.

FCN originally comprised a backbone of VGG 16 (Simonyan and Zisserman, 2014) CNN layers in the encoder and a single deconvolution layer in the encoder. The original paper proposes three versions, two with skip connections ( FCN -8 and FCN -16), and one without them (FCN-32). U-Net (Ronneberger et al., 2015), originally proposed for segmenting electron microscopy images, was rapidly adopted in the medical image segmentation literature. As its name suggests, it is a U-shaped model, with an encoder stacking convolutional layers that double in size filterwise, intercalated by pooling layers, and a symmetric decoder with pooling layers replaced by up-convolutions. Skip connections between corresponding encoder-decoder blocks improve the flow of information between layers, preserving low-level features lost during pooling and producing detailed segmentation boundaries.

U-Net frequently appears in the skin lesion segmentation literature both in its original form (Codella et al., 2017; Pollastri et al., 2020; Ramani and Ranjani, 2019) and modified forms (Tang et al., 2019a; Alom et al., 2019; Hasan et al., 2020), discussed below. Some works introduce their own models (Yuan et al., 2017; Al-Masni et al., 2018).

*******. Shortcut Connections. Connections between early and late layers in FCN s have been widely explored to improve both the forward and backward (gradient) information flow in the models, facilitating the training. The three most popular types of connections are described below.

Residual connections : Creating non-linear blocks that add their unmodified inputs to their outputs (He et al., 2016) alleviates gradient degradation in very deep networks. It provides a direct path for the gradient to flow through to the early layers of the network, while still allowing for very deep models. The technique appears often in skin lesion segmentation, in the implementation of the encoder (Sarker et al., 2018; Baghersalimi et al., 2019; Yu et al., 2017a) or both encoder and decoder (He et al., 2017; Venkatesh et al., 2018; Li et al., 2018a; Tu et al., 2019; Zhang et al., 2019a; He et al., 2018; Xue et al., 2018). Residual connections have also appeared in recurrent units (Alom et al., 2019, 2020), dense blocks (Song et al., 2019), chained pooling (He et al., 2017;

<!-- page_break -->

Li et al., 2018a; He et al., 2018), and 1-D factorized convolutions (Singh et al., 2019).

Skip connections appear in encoder-decoder architectures, connecting high-resolution features from the encoder's contracting path to the semantic features on the decoder's expanding path (Ronneberger et al., 2015). These connections help preserve localization, especially near region boundaries, and combine multi-scale features, resulting in sharper boundaries in the predicted segmentation. Skip connections are very popular in skin lesion segmentation because they are e GLYPH&lt;11&gt; ective and easy to implement (Zhang et al., 2019a; Baghersalimi et al., 2019; Song et al., 2019; Wei et al., 2019; Venkatesh et al., 2018; Azad et al., 2019; He et al., 2017; Alom et al., 2019; Sarker et al., 2018; Zeng and Zheng, 2018; Li et al., 2018a; Tu et al., 2019; Yu et al., 2017a; Singh et al., 2019; He et al., 2018; Xue et al., 2018; Alom et al., 2020; Vesal et al., 2018b; Liu et al., 2019b).

Dense connections expand the convolutional layers by connecting each layer to all its subsequent layers, concatenating their features (Huang et al., 2017). Iterative reuse of features in dense connections maximizes information flow forward and backward. Similar to deep supervision (Section 3.2.5), the gradient is propagated backwards directly through all previous layers. Several works (Zeng and Zheng, 2018; Song et al., 2019; Li et al., 2021c; Tu et al., 2019; Vesal et al., 2018b) integrated dense blocks in both the encoder and the decoder. Baghersalimi et al. (2019), Hasan et al. (2020) and Wei et al. (2019) used multiple dense blocks iteratively in only the encoder, while Li et al. (2018a) proposed dense deconvolutional blocks to reuse features from the previous layers. Azad et al. (2019) encoded densely connected convolutions into the bottleneck of their encoder-decoder to obtain better features.

*******. Convolutional Modules. As mentioned earlier, convolution not only provides a structural advantage, respecting the local connectivity structure of images in the output futures, but also dramatically improves parameter sharing since the parameters of a relatively small convolutional kernel are shared by all patches of a large image. Convolution is a critical element of deep segmentation models. In this section, we discuss some new convolution variants, which have enhanced and diversified this operation, appearing in the skin lesion segmentation literature.

Dilated convolution : In contrast to requiring full-resolution outputs in dense prediction networks, pooling and striding operations have been adopted in deep convolutional neural networks ( DCNN s) to increase the receptive field and diminish the spatial resolution of feature maps. Dilated or atrous convolutions are designed specifically for the semantic segmentation task to exponentially expand the receptive fields while keeping the number of parameters constant (Yu and Koltun, 2016). Dilated convolutions are convolutional modules with upsampled filters containing zeros between consecutive filter values. Sarker et al. (2018) and Jiang et al. (2019) utilized dilated residual blocks in the encoder to control the image field-of-view explicitly and incorporated multi-scale contextual information into the segmentation network. SkinNet (Vesal et al., 2018b) used dilated convolutions at the lower level of the network to enlarge the field-of-view and capture non-local information. Liu et al. (2019b) introduced dilated convolutions to the U-Net architecture, significantly improving the segmentation performance. Furthermore, di GLYPH&lt;11&gt; erent versions of the DeepLab architecture (Chen et al., 2017a,b, 2018a), which replace standard convolutions with dilated ones, have been used in skin lesion segmentation (Goyal et al., 2019a,b; Cui et al., 2019; Chen et al., 2018b; Canalini et al., 2019).

Separable convolution : Separable convolution or depth-wise separable convolution (Chollet, 2017) is a spatial convolution operation that convolves each input channel with its corresponding kernel. This is followed by a 1 GLYPH&lt;2&gt; 1 standard convolution to capture the

<!-- page_break -->

channel-wise dependencies in the output of depth-wise convolution. Depth-wise convolutions are designed to reduce the number of parameters and the computation of standard convolutions while maintaining the accuracy. DSNet (Hasan et al., 2020) and separable-Unet (Tang et al., 2019a) utilized depth-wise separable convolutions in the model to have a lightweight network with a reduced number of parameters. Adopted from the DeepLab architecture, Goyal et al. (2019b), Cui et al. (2019) and, Canalini et al. (2019) incorporated depth-wise separable convolutions in conjunction with dilated convolution to improve the speed and accuracy of dense predictions.

Global convolution : State-of-the-art segmentation models remove densely connected and global pooling layers to preserve spatial information required for full-resolution output recovery. As a result, by keeping high-resolution feature maps, segmentation models become more suitable for localization and, in contrast, less suitable for per-pixel classification, which needs transformation invariant features. To increase the connectivity between feature maps and classifiers, large convolutional kernels should be adopted. However, such kernels have a large number of parameters, which renders them computationally expensive. To tackle this, global convolutional network ( GCN ) modules adopt a combination of symmetric parallel convolutions in the form of 1 GLYPH&lt;2&gt; k + k GLYPH&lt;2&gt; 1 and k GLYPH&lt;2&gt; 1 + 1 GLYPH&lt;2&gt; k to cover a k GLYPH&lt;2&gt; k area of feature maps (Peng et al., 2017b). SeGAN (Xue et al., 2018) employed GCN modules with large kernels in the generator's decoder to reconstruct segmentation masks and in the discriminator architecture to optimally capture a larger receptive field.

Factorized convolution : Factorized convolutions (Wang et al., 2017) are designed to reduce the number of convolution filter parameters as well as the computation time through kernel decomposition when a high-dimensional kernel is substituted with a sequence of lower-dimensional convolutions. Additionally, by adding non-linearity between the composited kernels, the network's capacity may improve. FCA -Net (Singh et al., 2019) and MobileGAN (Sarker et al., 2019) utilized residual 1-D factorized convolutions (a sequence of k GLYPH&lt;2&gt; 1 and 1 GLYPH&lt;2&gt; k convolutions with ReLU non-linearity) in their segmentation architecture.

3.1.1.3. Multi-scale Modules. In FCN s, taking semantic context into account when assigning per-pixel labels leads to a more accurate prediction (Long et al., 2015). Exploiting multi-scale contextual information, e GLYPH&lt;11&gt; ectively combining them as well as encoding them in deep semantic segmentation have been widely explored.

Image Pyramid : RefineNet (He et al., 2017) and its extension (He et al., 2018), MSFCDN (Zeng and Zheng, 2018), FCA-Net (Singh et al., 2019), and Abraham and Khan (2019) fed a pyramid of multi-resolution skin lesion images as input to their deep segmentation network to extract multi-scale discriminative features. RefineNet (He et al., 2017, 2018), Factorized channel attention network (FCA-Net (Singh et al., 2019)) and Abraham and Khan (2019) applied convolutional blocks to di GLYPH&lt;11&gt; erent image resolutions in parallel to generate features which are then up-sampled in order to fuse multi-scale feature maps. Multi-scale fully convolutional DenseNets ( MSFCDN (Zeng and Zheng, 2018)) gradually integrated multi-scale features extracted from the image pyramid into the encoder's down-sampling path. Also, Jafari et al. (2016, 2017) extracted multi-scale patches from clinical images to predict semantic labels and refine lesion boundaries by deploying local and global information. While aggregating the feature maps computed at various image scales improves the segmentation performance, it also increases the computational cost of the network.

Parallel multi-scale convolutions : Alternatively, given a single image resolution, multiple convolutional filters with di GLYPH&lt;11&gt; erent kernel sizes (Zhang et al., 2019a; Wang et al., 2019a; Jahanifar et al., 2018) or multiple dilated convolutions with di GLYPH&lt;11&gt; erent dilation

<!-- page_break -->

rates (Goyal et al., 2019a,b; Cui et al., 2019; Chen et al., 2018b; Canalini et al., 2019) can be adopted in parallel paths to extract multi-scale contextual features from images. DSM (Zhang et al., 2019a) integrated multi-scale convolutional blocks into the skip connections of an encoder-decoder structure to handle di GLYPH&lt;11&gt; erent lesion sizes. Wang et al. (2019a) utilized multi-scale convolutional branches in the bottleneck of an encoder-decoder architecture, followed by attention modules to selectively aggregate the extracted multi-scale features.

Pyramid pooling : Another way of incorporating multi-scale information into deep segmentation models is to integrate a pyramid pooling ( PP ) module in the network architecture (Zhao et al., 2017). PP fuses a hierarchy of features extracted from di GLYPH&lt;11&gt; erent subregions by adopting parallel pooling kernels of various sizes, followed by up-sampling and concatenation to create the final feature maps. Sarker et al. (2018) and Jahanifar et al. (2018) utilized PP in the decoder to benefit from coarse-to-fine features extracted by di GLYPH&lt;11&gt; erent receptive fields from skin lesion images.

Dilated convolutions and skip connections are two other types of multi-scale information extraction techniques, which are explained in Sections ******* and *******, respectively.

*******. Attention Modules. An explicit way to exploit contextual dependencies in the pixel-wise labeling task is the self-attention mechanism (Hu et al., 2018; Fu et al., 2019). Two types of attention modules capture global dependencies in spatial and channel dimensions by integrating features among all positions and channels, respectively. Wang et al. (2019a) and Sarker et al. (2019) leveraged both spatial and channel attention modules to recalibrate the feature maps by examining the feature similarity between pairs of positions or channels and updating each feature value by a weighted sum of all other features. Singh et al. (2019) utilized a channel attention block in the proposed factorized channel attention ( FCA ) blocks, which was used to investigate the correlation of di GLYPH&lt;11&gt; erent channel maps for extraction of relevant patterns. Inspired by attention U-Net (Oktay et al., 2018), multiple works (Abraham and Khan, 2019; Song et al., 2019; Wei et al., 2019) integrated a spatial attention gate in an encoder-decoder architecture to combine coarse semantic feature maps and fine localization feature maps. Kaul et al. (2019) proposed FocusNet which utilizes squeeze-and-excitation blocks into a hybrid encoder-decoder architecture. Squeeze-and-excitation blocks model the channel-wise interdependencies to re-weight feature maps and improve their representation power. Experimental results demonstrate that attention modules help the network focus on the lesions and suppress irrelevant feature responses in the background.

*******. Recurrent Convolutional Neural Networks. Recurrent convolutional neural networks ( RCNN ) integrate recurrent connections into convolutional layers by evolving the recurrent input over time (Pinheiro and Collobert, 2014). Stacking recurrent convolutional layers ( RCL ) on top of the convolutional layer feature extractors ensures capturing spatial and contextual dependencies in images while limiting the network capacity by sharing the same set of parameters in RCL blocks. In the application of skin lesion segmentation, Attia et al. (2017) utilized recurrent layers in the decoder to capture spatial dependencies between deep-encoded features and recover segmentation maps at the original resolution. r N -Net (Alom et al., 2020), RU-Net, and R2U-Net (Alom et al., 2019) incorporated RCL blocks into the FCN architecture to accumulate features across time in a computationally e GLYPH&lt;14&gt; cient way and boosted the skin lesion boundary detection. Azad et al. (2019) deployed a non-linear combination of the encoder feature and decoder feature maps by adding a bi-convolutional LSTM BConvLSTM ( ) in skip connections. BConvLSTM consists of two independent convolutional LSTM modules ( ConvLSTMs ) which process the feature maps into two directions of backward and forward

<!-- page_break -->

paths and concatenate their outputs to obtain the final output. Modifications to the traditional pooling layers were also proposed, using a dense pooling strategy (Nasr-Esfahani et al., 2019).