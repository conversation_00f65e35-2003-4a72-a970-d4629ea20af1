## 4.1. Segmentation Annotation

Obtaining ground-truth segmentations is paramount for the objective evaluation of segmentation algorithms. For synthetically generated images (Section 2.2), ground-truth segmentations may be known by construction, either by applying parallel transformations to the original ground-truth masks in the case of traditional data augmentation, or by training generative models to synthesize images paired with their segmentation masks.

For images obtained from real patients, however, human experts have to provide the ground-truth segmentations. Various workflows have been proposed to reconcile the conflicting goals of ease of learning, speed, accuracy, and flexibility of annotation.

<!-- page_break -->

On one end of the spectrum, the expert traces the lesion by hand, on images of the skin lesion printed on photographic paper, which are then scanned (<PERSON><PERSON> et al., 2015). The technique is easy to learn and fast, but the printing and scanning procedure limits the accuracy, and the physical nature of the annotations makes corrections burdensome. On the other end of the spectrum, the annotation is performed on the computer, by a semi-automated procedure (<PERSON><PERSON> et al., 2019), with an initial border generated by a segmentation algorithm, which is then refined by the expert using an annotation software, by adjusting the parameters of the segmentation algorithm manually. This method is fast and easy to correct, but there might be a learning curve, and its accuracy depends on which algorithm is employed and how much the experts understand it.

By far, the commonest annotation method in the literature is somewhere in the middle, with fully manual annotations performed on a computer. The skin lesion image file may be opened either in a raster graphics editor (e.g., GNU Image Manipulation Program ( GIMP ) or Adobe Photoshop), or in a dedicated annotation software (Ferreira et al., 2012), where the expert traces the borders of the lesion using a mouse or stylus, with continuous freehand drawing, or with discrete control points connecting line segments (resulting in a polygon (Codella et al., 2019)) or smooth curve segments (e.g., cubic B-splines (Celebi et al., 2007a)). This method provides a good compromise, being easy to implement, fast, and accurate to perform, after an acceptable learning period for the annotator.


## 4.2. Inter-Annotator Agreement

Formally, dataset ground-truths can be viewed as samples of an estimator of the true label, which can never be directly observed (Smyth et al., 1995). This problem is often immaterial for classification, when annotation noise is small. However, in medical image segmentation, ground-truths su GLYPH&lt;11&gt; er from both biases (systematic deviations from the 'ideal') and significant noise (Zijdenbos et al., 1994; Chalana and Kim, 1997; Guillod et al., 2002; Grau et al., 2004; Bogo et al., 2015; Lampert et al., 2016), the latter appearing as inter-annotator (di GLYPH&lt;11&gt; erent experts) and intra-annotator (same expert at di GLYPH&lt;11&gt; erent times) variability.

In the largest study of its kind to date, Fortina et al. (2012) measured the inter-annotator variability among 12 dermatologists with varying levels of experience on a set of 77 dermoscopic images, showing that the average pairwise XOR dissimilarity (Section 4.3) between annotators was GLYPH&lt;25&gt; 15%, and that in 10% of cases, this value was &gt; 28%. They found more agreement among more experienced dermatologists than less experienced ones. Also, more experienced dermatologists tend to outline tighter borders than less experienced ones. They suggest that the level of agreement among experienced dermatologists could serve as an upper bound for the accuracy achievable by a segmentation algorithm, i.e., if even highly experienced dermatologists disagree on how to classify 10% of an image, it might be unreasonable to expect a segmentation algorithm to agree with more than 90% of any given ground-truth on the same image (Fortina et al., 2012).

Due to the aforementioned variability issues, whenever possible, skin lesion segmentation should be evaluated against multiple expert ground-truths, a good algorithm being one that agrees with the ground-truths at least as well as the expert agree among themselves (Chalana and Kim, 1997). Due to the cost of annotation, however, algorithms are often evaluated against a single ground-truth.

When multiple ground-truths are available, the critical issue is how to employ them. Several approaches have been proposed:

- GLYPH&lt;136&gt; Preferring one of the annotations (e.g., the one by the most experienced expert) and ignoring the others (Celebi et al., 2007a).

<!-- page_break -->

- GLYPH&lt;136&gt; Measuring and reporting the results for each annotator separately (Celebi et al., 2008), which might require non-trivial multivariate analyses if the aim is to rank the algorithms.
- GLYPH&lt;136&gt; Measuring each automated segmentation against all corresponding ground-truths and reporting the average result (Schaefer et al., 2011).
- GLYPH&lt;136&gt; Measuring each automated segmentation against an ensemble ground-truth formed by combining the corresponding groundtruths pixel-wise using a bitwise OR (Garnavi et al., 2011a; Garnavi and Aldeen, 2011), bitwise AND (Garnavi et al., 2011b), or a majority voting (Iyatomi et al., 2006, 2008; Norton et al., 2012).

The ground-truth ensembling process can be generalized using a thresholded probability map (Biancardi et al., 2010). First, all ground-truths for a sample are averaged pixel-wise into a probability map . Then the map is binarized, with the lesion corresponding to pixels greater than or equal to a chosen threshold. The operations of OR AND , , and majority voting, correspond, respectively to thresholds of 1 = n , 1, and ( n GLYPH&lt;0&gt; " = ) 2 n , with n being the number of ground-truths, and " being a small positive constant. AND and OR correspond, respectively, to the tightest and loosest possible contours, with other thresholds leading to intermediate results. While the optimal threshold value is data-dependent, large thresholds focus the evaluation on unambiguous regions, leading to overly optimistic evaluations of segmentation quality (Smyth et al., 1995; Lampert et al., 2016).

The abovementioned approaches fail to consider the di GLYPH&lt;11&gt; erences of experience or performance of the annotators (Warfield and Wells, 2004). More elaborate ground-truth fusion alternatives include shape averaging (Rohlfing and Maurer, 2006), border averaging (Chen and Parent, 1989; Chalana and Kim, 1997), binary label fusion algorithms such as STAPLE (Warfield and Wells, 2004), TESD (Biancardi et al., 2010), and SIMPLE (Langerak et al., 2010), as well as other more recent algorithms (Peng and Li, 2013; Peng et al., 2016, 2017a).

STAPLE (Simultaneous Truth And Performance Level Estimation) has been very influential in medical image segmentation evaluation, inspiring many variants. For each image and its ground-truth segmentations, STAPLE estimates a probabilistic true segmentation through the optimal combination of individual ground-truths, weighting each one by the estimated sensitivity and specificity of its annotator. STAPLE may fail when there are only a few annotators or when their performances vary too much (Langerak et al., 2010; Lampert et al., 2016), a situation addressed by SIMPLE (Selective and Iterative Method for Performance Level Estimation) (Langerak et al., 2010) by iteratively discarding poor quality ground-truths.

Instead of attempting to fuse multiple ground-truths into a single one before employing conventional evaluation metrics, the metrics themselves may be modified to take into account annotation variability. Celebi et al. (2009c) proposed the normalized probabilistic rand index ( NPRI ) (Unnikrishnan et al., 2007), a generalization of the rand index (Rand, 1971). It penalizes segmentation results more (less) in regions where the ground-truths agree (disagree). Fig. 9 illustrates the idea: ground-truths outlined by three experienced dermatologists appear in red, green, and blue, while the automated result appears in black. NPRI does not penalize the automated segmentation in the upper part of the image, where the blue border seriously disagrees with the other two (Celebi et al., 2009c). Despite its many desirable qualities, NPRI has a subtle flaw: it is non-monotonic with the fraction of misclassified pixels (Peserico and Silletti, 2010). Consequently, this index might be unsuitable for comparing poor segmentation algorithms.

<!-- page_break -->

Fig. 9: Sample segmentation results demonstrating inter-annotator disagreements. Note how annotator preferences can a GLYPH&lt;11&gt; ect the manual segmentations, e.g., smooth lesion borders (green), jagged lesion borders (black), oversegmented lesion (blue), etc. Figure taken from Celebi et al. (2009c) with permission.

picture_counter_23 The image shows a skin lesion with multiple colored outlines: black, red, green, and blue.