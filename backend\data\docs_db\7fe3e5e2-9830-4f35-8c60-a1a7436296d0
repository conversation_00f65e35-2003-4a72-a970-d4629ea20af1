## Related work

Recently, many works have been developed to detect and diagnose COVID-19 and other lung diseases based on different medical image modalities using different machine learning techniques especially deep learning and transfer learning. The purpose of all these works is to improve the performances of the methodologies used in the detection and classification of COVID-19 and other lung diseases. Where the proposed research will use X-ray images as a medical image modality, the focus in this section will be on the previous work based on X-rays.

<PERSON><PERSON><PERSON>, et al. 19 presented a system based on VGG16 to classify images of chest X-rays as healthy, COVID-19 pneumonia, and non-COVID-19 pneumonia. They applied the proposed system to 1248 X-ray images collected from 2 different public datasets. The collected X-ray images contain 500 healthy samples, 215 images for COVID19 pneumonia patients and 533 images for non-COVID-19 pneumonia patients. The achieved accuracy was 83.6%, while the sensitivity was 90.9%.

<PERSON><PERSON> et al. 20 applied deep learning to recognize COVID-19 cases using chest X-rays images. Transfer learning was used to train 4 CNN models which are DenseNet-121, SqueezeNet, ResNet50, and ResNet18 to binary classify images as COVID-19 or not. The training was applied to 84 (420 after augmentation) COVID-19 images and 2000 non-Covid images, while the test was applied to 100 COVID-19 images and 3000 non-COVID images. The best achieved sensitivity of these models was 98%, while the specificity was 92.9% for the SqueezeNet model.

<PERSON>hin 21 proposed a CNN model for binary classification of COVID-19 cases as COVID and Normal using chest X-ray images. Also, two pre-trained models which are ResNet50 and MobileNetv2 are applied to the used dataset of 13,824 X-ray images. The proposed CNN model achieved an accuracy of 96.71% and F1-score of

<!-- page_break -->

97%. MobileNetv2 achieved an accuracy of 95.73% and F1-score of 96%, while ResNet50 achieved an accuracy of 91.54% and F1-score of 91%.

Wang et al. 22 developed an open-source CNN called COVID-Net to detect COVID-19 cases using chest X-ray images. The proposed net can predict the case as one of three classes which are COVID-19 viral infection, non-COVID-19 infection, and normal. Also, an open access benchmark dataset COVIDx was introduced, it contains 13,975 X-ray images collected from 13,870 patients. The COVIDx dataset was generated using five different publicaly available datasets. The accuracy of COVID-Net reached 93.3%.

Panwar et al. 23 developed a deep learning model called nCOVnet for detecting COVID-19 based on X-rays. A dataset of 284 X-ray images was used of which 142 images are normal cases and 142 images are COVID-19 cases. The model achieved an accuracy of 88.1%.

Nigam et al. 24 used transfer learning to utilize 5 pre-trained models which are DenseNet121, NASNet, Xception, VGG16, and EfficientNet to classify Coronavirus suspected cases as normal, COVID-19 positive cases, and other classes. The used dataset contains 16,634 X-ray images, 6000 normal images, 5634 COVID images, and 5000 imges for others. The achieved accuracies were 79.01%, 85.03%, 88.03%, 89.96%, and 93.48% for VGG16, NASNet, Xception, DenseNet121, and EfficientNet respectively.

Chow et al. 25 used transfer learning to utilize 18 CNN models including VGG-19, VGG-16, ShufeNet, SqueezeNet. etc. to classify the cases as normal or COVID-19. The used dataset contains 700 X-ray images (350 normal cases and 350 COVID-19 cases) from both public and private institutes. The highest 4 models are VGG19, VGG-16, ResNet-101, and SqueezeNet with accuracy ranging from 90.7 to 94.3% and F1-score from 90.8 to 94.3%. The VGG-16 is the highest with an accuracy of 94.3% and F1-score of 94.3%. The majority voting of the 18 models and the highest 4 models achieved an accuracy of 93.0% and 94.0%, respectively.


## The proposed framework

In this section, the proposed framework has been explained. First, the used chest X-ray dataset has been described. Then, the developed framework, which includes 'pre-processing' phase and the 'Classification using CNN models based on transfer learning' phase, has been illustrated. Two different approaches have been used to train pre-trained CNN models using transfer learning. The first approach uses whole chest X-ray images, while the other approach uses lung-segmented images.