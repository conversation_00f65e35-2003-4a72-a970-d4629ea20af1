## 2.4. Image Preprocessing

Preprocessing may facilitate the segmentation of skin lesion images. Typical preprocessing operations include:

- GLYPH&lt;136&gt; Downsampling : Dermoscopy is typically a high-resolution technique, resulting in large image sizes, while many convolutional neural network ( CNN ) architectures, e.g., LeNet, AlexNet, VGG , GoogLeNet, ResNet, etc., require fixed-size input images, usually 224 GLYPH&lt;2&gt; 224 or 299 GLYPH&lt;2&gt; 299 pixels, and even those CNN s that can handle arbitrary-sized images (e.g., fullyconvolutional networks ( FCN s)) may benefit from downsampling for computational reasons. Downsampling is common in the skin lesion segmentation literature (<PERSON><PERSON> et al., 2017; <PERSON> et al., 2017a; <PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2019b; <PERSON><PERSON><PERSON> et al., 2020).
- GLYPH&lt;136&gt; Color space transformations : RGB images are expected by most models, but some works (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> and <PERSON>, 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> and <PERSON><PERSON>, 2020) employ alternative color spaces (<PERSON><PERSON> et al., 2008), such as CIELAB CIELUV , , and HSV . Often, one or more channels of the transformed space are combined with the RGB channels for reasons including, but not limited to, increasing the class separability, decoupling luminance and chromaticity, ensuring (approximate) perceptual uniformity, achieving invariance to illumination or viewpoint, and eliminating highlights.
- GLYPH&lt;136&gt; Additional inputs : In addition to color space transformations, recent works incorporate more focused and domain-specific inputs to the segmentation models, such as Fourier domain representation using the discrete Fourier transform (Tang et al., 2021b) and inputs based on the physics of skin illumination and imaging (Abhishek et al., 2020).
- GLYPH&lt;136&gt; Contrast enhancement : Insu GLYPH&lt;14&gt; cient contrast (Fig. 1(i)) is a prime reason for segmentation failures (Bogo et al., 2015), leading some works (Saba et al., 2019; Schaefer et al., 2011) to enhance the image contrast prior to segmentation.
- GLYPH&lt;136&gt; Color normalization : Varying illumination (Barata et al., 2015a,b) may lead to inconsistencies in skin lesion segmentation. This problem can be addressed by color normalization (Goyal et al., 2019b).
- GLYPH&lt;136&gt; Artifact removal : Dermoscopic images often present artifacts, among which hair (Fig. 1(g)) is the most distracting (Abbas et al., 2011), leading some studies ( ¨ nver and Ayan, 2019; Zafar et al., 2020; Li et al., 2021b) to remove it prior to U segmentation.

Classical machine learning models (e.g., nearest neighbors, decision trees, support vector machines (Celebi et al., 2007b, 2008; Iyatomi et al., 2008; Barata et al., 2014; Shimizu et al., 2015)), which rely on hand-crafted features (Barata et al., 2019), tend

<!-- page_break -->

to benefit more from preprocessing than DL models, which, when properly trained, can learn from the data how to bypass input issues (Celebi et al., 2015a; Valle et al., 2020). However, preprocessing may still be helpful when dealing with small or noisy datasets.


## 3. Model Design and Training

Multi-layer perceptrons ( MLP s) for pixel-level classification (Gish and Blanz, 1989; Katz and Merickel, 1989) appeared soon after the publication of the seminal backpropagation paper (Rumelhart et al., 1986), but these shallow feed-forward networks had many drawbacks (LeCun et al., 1998), including an excessive number of parameters, lack of invariance, and disregard for the inherent structure present in images.

CNN s are deep feedforward neural networks designed to extract progressively more abstract features from multidimensional signals (1-D signals, 2-D images, 3-D video, etc.) (LeCun et al., 2015). Therefore, in addition to addressing the aforementioned problems of MLP s, CNN s automate feature engineering (Bengio et al., 2013), that is, the design of algorithms that can transform raw signal values to discriminative features. Another advantage of CNNs over traditional machine learning classifiers is that they require minimal preprocessing of the input data. Due to their significant advantages, CNN s have become the method of choice in many medical image analysis applications over the past decade (Litjens et al., 2017). The key enablers in this deep learning revolution were: (i) the availability of massive data sets; (ii) the availability of powerful and inexpensive graphics processing units; (iii) the development of better network architectures, learning algorithms, and regularization techniques; and (iv) the development of open-source deep learning frameworks.

Semantic segmentation may be understood as the attempt to answer the parallel and complementary questions 'what' and 'where' in a given image. The former is better answered by translation-invariant global features, while the latter requires welllocalized features, posing a challenge to deep models. CNN s for pixel-level classification first appeared in the mid-2000s (Ning et al., 2005), but their use accelerated after the seminal paper on FCN s by Long et al. (2015), which, along with U-Net (Ronneberger et al., 2015), have become the basis for many state-of-the-art segmentation models. In contrast to classification CNN s (e.g., LeNet, AlexNet, VGG , GoogLeNet, ResNet), FCN s easily cope with arbitrary-sized input images.